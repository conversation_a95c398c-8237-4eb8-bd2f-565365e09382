on:
  push:
    branches:
      - main 

jobs:
  build:
    runs-on: macos-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          distribution: 'adopt'
          java-version: '17'
          
      - name: Set up Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.5'

      - name: Get dependencies
        run: flutter pub get

      - name: Build APK
        run: flutter build apk
#
      # - name: Build iOS
      #   run: |
      #     flutter build ios --no-codesign
      #     cd build/ios/iphoneos
      #     mkdir Payload
      #     cd Payload
      #     ln -s ../Runner.app
      #     cd ..
      #     zip -r app.ipa Payload
          
      - name: Push to Releases
        uses: ncipollo/release-action@v1
        with:
          artifacts: "build/app/outputs/apk/release/app-release.apk"
          # ,build/ios/iphoneos/app.ipa"
          tag: v1.0.${{ github.run_number }}
          token: ${{ secrets.TOKEN_CI }}

      - name: Install Firebase CLI
        run: npm install -g firebase-tools
      
      - name: Firebase login
        run: firebase login:ci --interactive

      - name: Upload to Firebase Distribution
        run: |
          # firebase appdistribution:distribute build/app/outputs/apk/release/app-release.apk build/ios/iphoneos/app.ipa --app "${{ secrets.FIREBASE_APP_ID }}" --token "$FIREBASE_TOKEN"
          firebase appdistribution:distribute build/app/outputs/apk/release/app-release.apk "${{ secrets.FIREBASE_APP_ID }}" --token "$FIREBASE_TOKEN"
