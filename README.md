![logo](https://github.com/StackdeansTeam/zawaj-mobile/assets/160779698/de5de0bd-200a-4768-b542-c2d2debe8b19)

Zawaj is Dating App for Android & IOS. It is built with <PERSON><PERSON> on Google's Flutter Framework.

![Flutter](https://img.shields.io/badge/Flutter-%2302569B.svg?style=for-the-badge&logo=Flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-039BE5?style=for-the-badge&logo=Firebase&logoColor=white)



## How To Start
#### 1. [Setup Flutter](https://flutter.io/setup/) [3.24.3] 04/11/2024 (Search For FVM)

#### 2. Clone the repo

```sh
$ git clone https://github.com/StackdeansTeam/zawaj-mobile
$ cd zawaj-mobile/
```

##### 3. Get all the packages

`flutter pub get`

##### 4. Run the build runner command

`flutter pub run build_runner build `

##### 5. Run the project

`flutter run` or simply press ` F5 key` if you are using VSCode

## Architecture

## to see the api requset in the debugger please : CTRL + SHIFT + F
- Search for `dio.interceptors.add`

--------------------------------
## Dependencies
- [x] [animated_splash_screen: ^1.3.0]
- [x] [another_carousel_pro: ^1.0.2]
- [x] [awesome_dio_interceptor: ^1.0.0]
- [x] [bloc: ^8.1.4]
- [x] [blur: ^3.1.0]
- [x] [bubble: ^1.2.1]
- [x] [cached_network: ^2.1.0]
- [x] [cached_network_image: ^3.3.1]
- [x] [camera: ^0.10.5+9]
- [x] [carousel_slider: ^4.2.1]
- [x] [chalk: null]
- [x] [chat_bubbles]
- [x] [circular_countdown_timer: ^0.2.3]
- [x] [cloud_firestore: ^4.15.10]
- [x] [connectivity_plus: ^5.0.2]
- [x] [cupertino_icons: ^1.0.2]
- [x] [dartz: ^0.10.1]
- [x] [dio: null]
- [x] [easy_localization: ^3.0.3]
- [x] [equatable: ^2.0.5]
- [x] [expandable: ^5.0.1]
- [x] [fancy_password_field: ^2.0.3]
- [x] [firebase_auth: ^4.18.0]
- [x] [firebase_core: ^2.27.2]
- [x] [firebase_messaging: ^14.7.21]
- [x] [firebase_storage: ^11.6.11]
- [x] [flick_video_player: ^0.8.0]
- [x] [flutter: sdk: flutter]
- [x] [flutter_bloc: ^8.1.5]
- [x] [flutter_facebook_auth: ^6.2.0]
- [x] [flutter_neat_and_clean_calendar: ^0.3.18+40]
- [x] [flutter_phoenix: ^1.1.1]
- [x] [flutter_screenutil: ^5.9.0]
- [x] [flutter_svg: ^2.0.7]
- [x] [fluttertoast: ^8.2.2]
- [x] [get_it: null]
- [x] [giphy_get: ^3.5.5]
- [x] [google_sign_in: ^6.2.1]
- [x] [insta_assets_picker: ^2.2.1]
- [x] [internet_connection_checker: null]
- [x] [intl: ^0.18.1]
- [x] [onesignal_flutter: ^5.1.5]
- [x] [password_strength_indicator: ^1.0.1]
- [x] [percent_indicator: ^4.0.1]
- [x] [permission_handler: ^11.3.0]
- [x] [pin_code_fields: ^8.0.1]
- [x] [popover: ^0.3.0]
- [x] [readmore: ^2.2.0]
- [x] [share_plus: ^7.2.1]
- [x] [shared_preferences: ^2.1.0]
- [x] [shimmer: ^3.0.0]
- [x] [sign_in_with_apple: ^5.0.0]
- [x] [signalr_core: ^1.1.2]
- [x] [smooth_page_indicator: ^1.1.0]
- [x] [socket_io_client: ^2.0.3+1]
- [x] [url_launcher: ^6.2.3]
- [x] [video_player: ^2.8.2]
- [x] [visibility_detector: ^0.4.0+2]
- [x] [webview_flutter: ^4.7.0]
- [x] [webviewx_plus: ^0.5.0]
- [x] [wechat_assets_picker: ^9.0.0]
- [x] [appinio_video_player: ^1.2.2]
- [x] [sliding_widget: ^0.0.5]
- [x] [action_slider: ^0.7.0]
- [x] [meta: ^1.11.0]
- [x] [flutter_image_editor: ^2.1.0]
- [x] [image_picker: ^1.1.1]
- [x] [google_fonts: ^6.2.1]
- [x] [dotted_border: ^2.1.0]


--------------------------

# Testing credit card
- Card Number: ****************
- Validity: 12/25
- CVV: 125









