name: zawaj
description: "A new Flutter project."

publish_to: "none"

version: 1.0.45+45

environment:
  sdk: ">=3.1.0 <4.0.0"

dependencies:
  action_slider: ^0.7.0
  animated_floating_widget: ^1.0.0
  animated_menu:
    path: packages/animated_menu
  animated_splash_screen: ^1.3.0
  another_carousel_pro: ^1.0.2
  awesome_dio_interceptor: ^1.0.0
  bloc: ^8.1.4
  blur: ^3.1.0
  bubble: ^1.2.1
  cached_network: ^2.1.0
  cached_network_image: ^3.3.1
  camera: ^0.11.0+2
  carousel_slider: ^5.0.0
  chalk: null
  chat_bubbles: null
  circular_countdown_timer: ^0.2.3
  cloud_firestore:
  connectivity_plus: ^5.0.2
  cupertino_icons: ^1.0.2
  dartz: ^0.10.1
  dio: null
  dotted_border: ^2.1.0
  dropdown_button2: ^2.3.9
  easy_localization: ^3.0.3
  equatable: ^2.0.5
  expandable: ^5.0.1
  fancy_password_field: ^2.0.3
  firebase_auth:
  firebase_core:
  firebase_messaging: ^16.0.0
  firebase_storage:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.5
  flutter_facebook_auth: ^6.2.0
#  flutter_image_editor: ^2.1.0
  flutter_neat_and_clean_calendar: ^0.3.18+40
  flutter_phoenix: ^1.1.1
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.7
  fluttertoast: ^8.2.12
  get_it: null
  giphy_get: ^3.5.5
  google_fonts: null
  google_sign_in: ^6.2.1
  image_cropper: ^8.0.2
  image_picker: ^1.1.1
  infinite_scroll_pagination: ^4.0.0
  insta_assets_picker: ^3.3.0
#  insta_assets_picker: ^2.2.1
  internet_connection_checker: null
  meta: ^1.11.0
  onesignal_flutter: ^5.3.4
  password_strength_indicator: ^1.0.1
  percent_indicator: ^4.0.1
  permission_handler: ^11.3.0
  pin_code_fields: ^8.0.1
  popover: ^0.3.0
  readmore: ^2.2.0
  shared_preferences: ^2.1.0
  shimmer: ^3.0.0
  sign_in_with_apple: ^7.0.1
  signalr_core: ^1.1.2
  sliding_widget: ^0.0.5
  smooth_page_indicator: ^1.1.0
  socket_io_client: ^2.0.3+1
  timezone: ^0.9.4
  url_launcher: ^6.2.3
  visibility_detector: ^0.4.0+2
#  webview_flutter: ^4.10.0
  webviewx_plus: ^0.5.0
  wechat_assets_picker: ^9.0.0
  sqflite_android: ^2.4.0

dependency_overrides:
  webview_flutter_wkwebview: ^3.16.3
  camera: ^0.11.0+2
  camera_avfoundation: ^0.9.17+5
  intl: ^0.20.2
  fluttertoast: ^8.2.12
  web_socket_channel: ^3.0.3
  insta_assets_crop: ^0.1.1
  sqflite_android: 2.4.0

dev_dependencies:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/languages/
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
