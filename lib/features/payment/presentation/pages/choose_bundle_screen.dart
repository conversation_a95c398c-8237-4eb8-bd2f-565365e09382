import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/dimensions.dart';
import 'package:zawaj/core/constants/image_manager.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/helper/cache_helper.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/build_dialog.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_button.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/core/widgets/loading_circle.dart';
import 'package:zawaj/features/payment/presentation/bloc/payment_bloc.dart';
import 'package:zawaj/features/payment/presentation/pages/payment_possibilities.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:zawaj/features/profile/presentation/pages/verification/presentation/verifing_request_sent_screen.dart';

class ChooseBundle extends StatefulWidget {
  const ChooseBundle(
      {super.key, required this.userId, required this.isFromProfileScreen});
  final String userId;
  final bool isFromProfileScreen;

  @override
  State<ChooseBundle> createState() => _ChooseBundleState();
}

class _ChooseBundleState extends State<ChooseBundle> {
  @override
  void initState() {
    super.initState();
    
    // Temporary fix: Add Name placeholder to prevent API errors
    if (ProfileBloc.get(context).profileData?.name == null || 
        ProfileBloc.get(context).profileData?.name?.isEmpty == true) {
      // Set a temporary name placeholder
      CacheHelper.setData(key: Strings.Name, value: 'Name');
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(30),
        child: CustomButton(
            onTap: () {
              MagicRouter.navigateTo(PayementPossibility(
                isFromProfileScreen: widget.isFromProfileScreen,
              ));
            },
            text: Strings.continu),
      ),
      isFullScreen: true,
      child: const SingleChildScrollView(
        child: Column(
          children: [
            CustomAppBar(
              isLogoTitle: true,
            ),
            SizedBox(
              height: 50,
            ),
            CustomText(
              text: Strings.golden_bundle,
              fontSize: Dimensions.largeFont,
              fontWeight: FontWeight.bold,
            ),
            SizedBox(
              height: 30,
            ),
            SizedBox(
              height: 5,
            ),
            CustomText(
              align: TextAlign.start,
              text: Strings.free_bundle_instructions,
              color: ColorManager.darkGrey,
            )
          ],
        ),
      ),
    );
  }
}
