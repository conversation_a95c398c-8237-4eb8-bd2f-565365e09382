class ChatMessage {
  final int messageId;
  final String senderId;
  final String recipientId;
  final int chatId;
  final String content;
  final bool isActive;
  final int isChatEnded;
  final int daysToEnd;
  final DateTime sentAt;

  ChatMessage({
    required this.messageId,
    required this.senderId,
    required this.recipientId,
    required this.chatId,
    required this.content,
    required this.sentAt,
    required this.isActive,
    required this.isChatEnded,
    required this.daysToEnd,
  });
}

class MeetingRequestModel {
  int meetingRequestID;
  int? chatID;
  String? senderUserID;
  String? receiverUserID;
  int? meetingRequestStatusID;
  String? sendDate;
  String? receiverAnswerDate;
  bool isAddMeetingRequest;
  bool isUpdateByAdmin;

  MeetingRequestModel({
    required this.meetingRequestID,
    this.chatID,
    this.senderUserID,
    this.receiverUserID,
    this.meetingRequestStatusID,
    this.sendDate,
    this.receiverAnswerDate,
    required this.isAddMeetingRequest,
    this.isUpdateByAdmin = false,
  });

  factory MeetingRequestModel.fromJson(Map<String, dynamic> json) {
    return MeetingRequestModel(
      meetingRequestID: json["meetingRequestID"] ?? 0,
      chatID: json["chatID"],
      senderUserID: json["senderUserID"],
      receiverUserID: json["receiverUserID"],
      meetingRequestStatusID: json["meetingRequestStatusID"],
      sendDate: json["sendDate"],
      receiverAnswerDate: json["receiverAnswerDate"],
      isAddMeetingRequest: json["isAddMeetingRequest"] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "MeetingRequestID": meetingRequestID,
      "ChatID": chatID,
      "SenderUserID": senderUserID,
      "ReceiverUserID": receiverUserID,
      "MeetingRequestStatusID": meetingRequestStatusID,
      "SendDate": sendDate,
      "ReceiverAnswerDate": receiverAnswerDate,
      "IsAddMeetingRequest": isAddMeetingRequest,
      "IsUpdateByAdmin": isUpdateByAdmin,
    };
  }
}

class ChatData {
  final DateTime chatCreatedAt;
  final bool isUserDeleted;
  MeetingRequestModel meetingRequest;
  bool isCanSendMeetingRequest;
  final List<ChatMessage> messages;

  ChatData({
    required this.chatCreatedAt,
    required this.isUserDeleted,
    required this.isCanSendMeetingRequest,
    required this.meetingRequest,
    required this.messages,
  });
}


