import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'dart:ui';

import 'package:action_slider/action_slider.dart';
import 'package:animated_floating_widget/animated_floating_widget.dart';
import 'package:animated_menu/animated_menu.dart';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:giphy_get/giphy_get.dart';
import 'package:signalr_core/signalr_core.dart';
import 'package:video_player/video_player.dart';

import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/end_points.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/extensions/snack_bar.dart';
import 'package:zawaj/core/helper/cache_helper.dart';
import 'package:zawaj/core/helper/signalr.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/core/widgets/loading_circle.dart';
import 'package:zawaj/core/widgets/online_status_widget.dart';
import 'package:zawaj/features/chat/presentation/chat_message_cubit/chat_messages_cubit.dart';
import 'package:zawaj/features/chat/presentation/chat_message_cubit/chat_messages_states.dart';
import 'package:zawaj/features/chat/presentation/widgets/message_send_bar_widget.dart';
import 'package:zawaj/features/chat/presentation/widgets/profile_image_widget.dart';
import 'package:zawaj/features/chat/presentation/widgets/reason_alert_dialog.dart';
import 'package:zawaj/features/chat/presentation/widgets/reciever_bubble.dart';
import 'package:zawaj/features/chat/presentation/widgets/sender_bubble.dart';
import 'package:zawaj/features/chat/presentation/widgets/sequential_dialogs_widget.dart';
import 'package:zawaj/features/chat/presentation/widgets/video_action_slider_widget.dart';
import 'package:zawaj/features/dashboard/view.dart';
import 'package:zawaj/features/home/<USER>/models/home_model.dart';
import 'package:zawaj/features/home/<USER>/pages/partner_details_by_id_screen.dart';
import 'package:zawaj/features/home/<USER>/pages/partner_details_screen.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';

import '../../data/data_source/chat_messages_dataSource.dart';
import '../../data/model/chat_messages_model.dart';
import '../../data/repos/chat_message_repository.dart';
import '../../domain/get_chat_messages_usecase.dart';

class ChatScreen extends StatefulWidget {
  final String receiverId;
  final String receiverProdileImage;
  final String receiverName;
  final HomeModel? homeModel;

  const ChatScreen({
    super.key,
    required this.receiverId,
    required this.receiverProdileImage,
    this.homeModel,
    required this.receiverName,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _sendMessgeController = TextEditingController();
  final List<String> _messages = [];
  final ScrollController _scrollController = ScrollController();
  late HubConnection _hubConnection;
  String _connectionId = '';
  GiphyGif? _selectedGif;
  bool _isSending = false;
  bool showArrowIcon = false;
  late CameraController _cameraController;
  late List<CameraDescription> _cameras;
  bool _isRecording = false;
  bool _isSwitchingCamera = false;
  Timer? _meetingRequestTimer;
  bool _isMeetingRequestDialogOpen = false;
  bool _isMeetingRequestPending = false;

  XFile? _videoFile;
  late Timer _recordingTimer;
  bool isNewMessageReceived = false;
  int _selectedCameraIndex = 0;
  final actionSliderController = ActionSliderController();

  late GetChatMessagesDataUseCase _getChatMessagesDataUseCase;
  int currentPage = 1;
  bool canFetchMoreMessages = true;
  ChatData? chatData;
  bool isDisplayMeetingRequestForReceiver = false;
  String closingChatMessage = '';
  bool isLoadingMore = false;
  @override
  void initState() {
    super.initState();
    final chatMessagesRepository =
        ChatMessagesRepositoryImpl(ChatDataProvider());
    _getChatMessagesDataUseCase =
        GetChatMessagesDataUseCase(chatMessagesRepository);

    loadInitialChatData();
    log('widget.receiverId ${widget.receiverId}');
    _initSignalR();
    CacheHelper.setData(
        key: Strings.receiverUserIdChat, value: widget.receiverId);
    
    _meetingRequestTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      if (mounted) {
        _checkForMeetingRequest();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _meetingRequestTimer?.cancel();
    CacheHelper.removeData(key: Strings.receiverUserIdChat);
    super.dispose();
  }

  Future<void> loadInitialChatData() async {
    try {
      final initialChatData = await _getChatMessagesDataUseCase
          .call(widget.receiverId, page: currentPage);
      if (mounted) {
        setState(() {
          chatData = initialChatData;
          closingChatMessage = chatData!.meetingRequest.meetingRequestStatusID! >= 3 ? 'تم إغلاق المحادثة مع الطّرف الاخر بسبب إنتقالكما إلى مرحلة متقدمة (لقاء زوم)' : chatData!.messages[0].isChatEnded == 1 ? 'لقد أنتهت المحادثة لايمكنك ارسال أو استقبال رسائل' : '';
          isDisplayMeetingRequestForReceiver = chatData!.meetingRequest.receiverUserID == ProfileBloc.get(context).profileData!.id 
                                              && chatData!.messages[0].isChatEnded == 0 
                                              && chatData!.meetingRequest.meetingRequestStatusID == 1 
                                              ? true : false;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _initializeCamera() async {
    _cameras = await availableCameras();
    _cameraController = CameraController(_cameras[0], ResolutionPreset.high);
    await _cameraController.initialize();
  }

  Future<void> fetchMoreMessages(String user2Id) async {
    if (canFetchMoreMessages) {
      canFetchMoreMessages = false;
      try {
        final newMessages =
            await _getChatMessagesDataUseCase.call(user2Id, page: currentPage);

        if (mounted) {
          setState(() {
            chatData!.messages.addAll(newMessages.messages);
            currentPage++;
          });
        }
      } catch (e) {
        // Handle error silently
      } finally {
        if (mounted) {
          setState(() {
            canFetchMoreMessages = true;
          });
        }
      }
    }
  }

  Future<void> _startRecording() async {
    try {
      await _initializeCamera();

      await _cameraController.startVideoRecording();
      setState(() {
        _isRecording = true;
      });

      _recordingTimer = Timer(const Duration(seconds: 41), _stopRecording);
    } catch (e) {
      log('Error starting video recording: $e');
    }
  }

  Future<void> _stopRecording() async {
    try {
      setState(() {
        _isSending = true;
      });
      XFile videoFile = await _cameraController.stopVideoRecording();
      setState(() {
        _isRecording = false;
        _videoFile = videoFile;
      });
    } catch (e) {
      log('Error stopping video recording: $e');
    } finally {
      _recordingTimer.cancel();
      _cameraController.dispose();
      setState(() {
        _isSending = false;
      });
    }
  }

  Future<void> _switchCamera() async {
    try {
      setState(() {
        _isSwitchingCamera = true;
      });

      if (_isRecording) {
        XFile videoFile = await _cameraController.stopVideoRecording();
        setState(() {
          _isRecording = false;
          _videoFile = videoFile;
        });
      }

      await _cameraController.dispose();

      _selectedCameraIndex = (_selectedCameraIndex + 1) % _cameras.length;

      _cameraController = CameraController(
        _cameras[_selectedCameraIndex],
        ResolutionPreset.high,
      );

      await _cameraController.initialize();

      setState(() {});

      await _cameraController.startVideoRecording();
      setState(() {
        _isRecording = true;
      });
    } catch (e) {
      log('Error switching camera during recording: $e');
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  Future<void> _sendVideo(XFile videoFile) async {
    try {
      String url = '${EndPoints.BASE_URL}${EndPoints.convertFile}';
      String newPath = videoFile.path.replaceAll('.temp', '.mp4');
      File newFile = await File(videoFile.path).rename(newPath);
      FormData formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(newFile.path),
      });
      setState(() {
        _isSending = true;
      });
      Response response = await Dio().post(url, data: formData);
      if (response.statusCode == 200) {
        String path = response.data['path'];
        _sendMessage('${EndPoints.BASE_URL_image}$path', true);
        setState(() {
          _isSending = false;
        });
        _discardVideo();
      } else {
        log('Error sending video: ${response.statusCode}');
      }
    } catch (e) {
      log('Error sending video: $e');
    }
  }

  void _discardVideo() {
    setState(() {
      _videoFile = null;
    });
  }

  void _initSignalR() async {
    // SignalR is now initialized at the dashboard level
    // Just get the existing connection
    if (SignalRService.connection == null) {
      log('Error: SignalR connection is null. Waiting for connection...');
      // Wait a bit for the connection to be established
      await Future.delayed(const Duration(seconds: 2));
    }
    _hubConnection = SignalRService.connection!;
    log('Using SignalR connection: ${_hubConnection.connectionId}');
    
    _hubConnection.on("ReceiveMessage", (List<dynamic>? args) {
      log('===========> Received message args: $args');

      if (args != null && args.length >= 2) {
        final senderId = args[0].toString();
        final message = args[1].toString();
        final profileId = ProfileBloc.get(context).profileData?.id;
        if (profileId == null) {
          log("Error: Profile ID is null when receiving message");
          return;
        }
        
        log('Received message from $senderId: $message');
        setState(() {
          _messages.add(message);
          if (chatData != null) {
            final newMessage = ChatMessage(
              messageId: DateTime.now().millisecondsSinceEpoch,
              senderId: senderId,
              recipientId: profileId,
              chatId: chatData!.messages.first.chatId,
              content: message,
              sentAt: DateTime.now(),
              isActive: true,
              isChatEnded: 0,
              daysToEnd: chatData!.messages.first.daysToEnd,
            );
            chatData!.messages.insert(0, newMessage);
          }
        });
      } else {
        log('Invalid message args received: $args');
      }
    });

    try {
      _connectionId = _hubConnection.connectionId!;
    } catch (e) {
      log("Error getting connection ID: $e");
    }
  }

  bool _containsRestrictedContent(String message) {
    // Phone number patterns (various formats)
    final phonePatterns = [
      RegExp(r'0\d{8,}'), // Starting with 0 followed by 8+ digits
      RegExp(r'\+\d{10,}'), // International format
      RegExp(r'\d{3}[-.\s]?\d{3}[-.\s]?\d{4}'), // XXX-XXX-XXXX format
      RegExp(r'\d{10,}'), // 10+ consecutive digits
      RegExp(r'\d{3,}'), // 3 or more consecutive digits
    ];
    
    // Communication-related keywords (Arabic and English)
    final communicationKeywords = [
      // Arabic
      'واتساب', 'واتس اب','انستا', 'whatsapp', 'تليجرام', 'telegram', 'فيسبوك', 'facebook',
      'انستجرام', 'instagram', 'سناب شات', 'snapchat', 'تيك توك', 'tiktok',
      'ايميل', 'email', 'بريد الكتروني', 'جيميل', 'gmail', 'ياهو', 'yahoo',
      'سكايب', 'skype', 'زوم', 'zoom', 'فايبر', 'viber', 'لاين', 'line',
      'كيك', 'kik', 'ديسكورد', 'discord', 'تويتر', 'twitter', 'لينكد ان', 'linkedin',
      // Contact methods
      'رقم', 'رقمي', 'هاتف', 'جوال', 'موبايل', 'تلفون', 'اتصل', 'اتصال','رقمك','تلفونك',
      'راسلني', 'تواصل', 'تواصلي', 'اضافة', 'اضيفيني', 'اضافتي',
      // English equivalents
      'number', 'phone', 'mobile', 'call', 'contact', 'add me', 'message me',
      'reach me', 'dm me', 'text me', 'chat me', 'follow me',
    ];
    
    final lowerMessage = message.toLowerCase();
    
    // Check for phone numbers
    for (final pattern in phonePatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }
    
    // Check for communication keywords
    for (final keyword in communicationKeywords) {
      if (lowerMessage.contains(keyword.toLowerCase())) {
        return true;
      }
    }
    
    // Check for @ symbols (social media handles/emails)
    if (message.contains('@')) {
      return true;
    }
    
    // Check for common domain extensions
    final domainPatterns = [
      RegExp(r'\.(com|org|net|edu|gov|mil|int|co|me|tv|info|biz)', caseSensitive: false),
      RegExp(r'www\.', caseSensitive: false),
      RegExp(r'http[s]?://', caseSensitive: false),
    ];
    
    for (final pattern in domainPatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }
    
    return false;
  }

  void _sendMessage(String message, bool isVideoRecording) {
    GiphyGif? gifFile = _selectedGif;
    if (message.isNotEmpty || gifFile != null) {
      // Check for restricted content
      if (!isVideoRecording && _containsRestrictedContent(message)) {
        context.getSnackBar(
          snackText: 'المشترك الكريم!\nنحن في تطبيق زواج 48 ننصحك بالتّواصل مع الطّرف الآخر من خلال التّطبيق فقط حفاظًا على الخصوصيّة والأمان، إذا احتجت مساعدة أو استشارة ما تواصل معنا.',
          isError: true,
        );
        return;
      }
      
      // Preserve newlines in the message
      message = message.trim();
      
      final profileId = ProfileBloc.get(context).profileData?.id;
      if (profileId == null) {
        context.getSnackBar(
          snackText: 'حدث خطأ في إرسال الرسالة',
          isError: true,
        );
        return;
      }
      
      // Add message to local state immediately
      if (chatData != null) {
        final newMessage = ChatMessage(
          messageId: DateTime.now().millisecondsSinceEpoch, // Temporary ID
          senderId: profileId,
          recipientId: widget.receiverId,
          chatId: chatData!.messages.first.chatId,
          content: message,
          sentAt: DateTime.now(),
          isActive: true,
          isChatEnded: 0,
          daysToEnd: chatData!.messages.first.daysToEnd,
        );
        
        setState(() {
          chatData!.messages.insert(0, newMessage);
        });
      }
      
      log('Sending message to ${widget.receiverId}: $message');
      _hubConnection.invoke(
        args: [
          profileId,
          widget.receiverId,
          message
        ],
        "SendMessage",
      );
      _sendMessgeController.clear();
      
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.minScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _endChat(ChatMessagesCubit chatMessgaeCubit, int chatId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ReasonAlertDialog(
          title: 'طلب إنهاء المحادثة',
          onSubmit: (String reason) async {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) => const Center(
                child: CircularProgressIndicator(),
              ),
            );

            try {
              await chatMessgaeCubit.endChat(chatId: chatId, reason: reason);
              // Clean up chat state
              await CacheHelper.removeData(key: Strings.receiverUserIdChat);
              await _hubConnection.stop();
              
              Navigator.pop(context); // Close the loading dialog
              Navigator.pop(context, true); // Close the reason dialog
              ProfileBloc.get(context).getMyProfile();
            } catch (e) {
              Navigator.pop(context); // Close the loading dialog on error
              context.getSnackBar(
                snackText: 'حدث خطأ أثناء إنهاء المحادثة',
                isError: true,
              );
            }
          },
        );
      },
    ).then((value) {
      if (value == true) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          context.getSnackBar(
            snackText: 'لقد تم إنهاء المحادثة بنجاح',
            isError: false,
          );

          Future.delayed(const Duration(seconds: 1), () {
            MagicRouter.navigateAndPopAll(
              const DashBoardScreen(initialIndex: 1),
            );
          });
        });
      }
    });
  }

  void _showMeetingRequestConfirmationDialog(
      BuildContext context, ChatMessagesCubit chatMessgaeCubit, int chatId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد طلب المقابلة'),
          content: const Text(
              'لقد قمتم بطلب مقابلة مع الطرف الاخر عبر منظومة زواج ٤٨. في حال تم الموافقة على لقاء الزوم، سيتم اغلاق المحادثة هنا وسيتم التواصل معكما عبر المكتب للتنسيق.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _sendMeetingRequest(chatMessgaeCubit, chatId);
              },
              child: const Text('إرسال'),
            ),
          ],
        );
      },
    );
  }

  void _sendMeetingRequest(
      ChatMessagesCubit chatMessgaeCubit, int chatId) async {
    if (ProfileBloc.get(context).profileData!.id != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      try {
        MeetingRequestModel meetingRequest = MeetingRequestModel(
          meetingRequestID: 0,
          chatID: chatId,
          senderUserID: ProfileBloc.get(context).profileData!.id,
          receiverUserID: widget.receiverId,
          meetingRequestStatusID: 1,
          receiverAnswerDate: null,
          isAddMeetingRequest: true,
          isUpdateByAdmin: false,
        );
        final response =
            await chatMessgaeCubit.sendMeetingRequest(meetingRequest);
        final responseData = response.data;
        Navigator.pop(context);
        meetingRequest.meetingRequestID = responseData['result'];
        meetingRequest.isAddMeetingRequest = false;
        chatData!.meetingRequest = meetingRequest;
        setState(() {
          chatData!.isCanSendMeetingRequest = false;
        });
        context.getSnackBar(
          snackText: responseData['message'],
          isError: false,
        );
      } catch (e) {
        Navigator.pop(context);
        if (e is DioException) {
          final responseData = e.response?.data;
          context.getSnackBar(
            snackText: responseData['message'],
            isError: true,
          );
        }
      }
    }
  }

  Future<void> _checkForMeetingRequest() async {
    try {
      final initialChatData = await _getChatMessagesDataUseCase
          .call(widget.receiverId, page: currentPage);
      
      if (mounted) {
        final bool shouldShowRequest = initialChatData.meetingRequest.receiverUserID == ProfileBloc.get(context).profileData!.id 
                                     && initialChatData.messages[0].isChatEnded == 0 
                                     && initialChatData.meetingRequest.meetingRequestStatusID == 1;
        
        if (shouldShowRequest && !isDisplayMeetingRequestForReceiver) {
          setState(() {
            chatData = initialChatData;
            isDisplayMeetingRequestForReceiver = true;
          });
          
          final latestMeetingRequestId = initialChatData.meetingRequest.meetingRequestID;
          
          final chatMessgaeCubit = BlocProvider.of<ChatMessagesCubit>(context);
          _showMeetingRequestDialog(context, chatMessgaeCubit);
        }
      }
    } catch (e) {
      // Handle error silently
      log('Error checking meeting request: $e');
    }
  }

  void _showMeetingRequestDialog(BuildContext context, ChatMessagesCubit chatMessgaeCubit) {
    if (_isMeetingRequestDialogOpen) return;
    
    setState(() {
      _isMeetingRequestDialogOpen = true;
      _isMeetingRequestPending = true;
    });

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('طلب لقاء'),
        content: const Text('هل تريد الموافقة على طلب اللقاء؟'),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _isMeetingRequestDialogOpen = false;
                _isMeetingRequestPending = false;
              });
              Navigator.pop(context);
              _sendMeetingRequestAnswer(chatMessgaeCubit, false);
            },
            child: const Text('غير موافق'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isMeetingRequestDialogOpen = false;
                _isMeetingRequestPending = true; // Keep it true since we're accepting
              });
              Navigator.pop(context);
              _sendMeetingRequestAnswer(chatMessgaeCubit, true);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    ).then((_) {
      if (mounted) {
        setState(() {
          _isMeetingRequestDialogOpen = false;
        });
      }
    });
  }

  void _sendMeetingRequestAnswer(
      ChatMessagesCubit chatMessgaeCubit, bool isAccept) async {
    if (ProfileBloc.get(context).profileData!.id != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (_) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      try {
        // Get the latest chat data to ensure we have the most recent meeting request ID
        final latestChatData = await _getChatMessagesDataUseCase.call(widget.receiverId, page: currentPage);
        
        // Create a complete meeting request model with all required fields
        final currentDate = DateFormat('yyyy/MM/dd HH:mm').format(DateTime.now());
        final myUserId = ProfileBloc.get(context).profileData!.id;
        
        MeetingRequestModel meetingRequest = MeetingRequestModel(
          meetingRequestID: latestChatData.meetingRequest.meetingRequestID, // Use the latest meeting request ID
          chatID: latestChatData.messages.first.chatId,
          senderUserID: widget.receiverId, // The sender is the one who sent the request
          receiverUserID: myUserId, // We are the receiver since we're answering
          meetingRequestStatusID: isAccept ? 3 : 2,
          receiverAnswerDate: currentDate,
          sendDate: latestChatData.meetingRequest.sendDate ?? currentDate,
          isAddMeetingRequest: false,
          isUpdateByAdmin: false,
        );

        final response = await chatMessgaeCubit.sendMeetingRequestAnswer(meetingRequest);
        final responseData = response.data;
        
        setState(() {
          chatData!.meetingRequest = meetingRequest; // Update the entire meeting request object
          chatData!.isCanSendMeetingRequest = isAccept ? false : true;
        });
        closingChatMessage = isAccept ? 'تم إغلاق المحادثة مع الطّرف الاخر بسبب إنتقالكما إلى مرحلة متقدمة (لقاء زوم)' : closingChatMessage;
        Navigator.pop(context);
        context.getSnackBar(
          snackText: responseData['message'],
          isError: false,
        );
        if (isAccept) {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (_) => const Center(
                child: CircularProgressIndicator(),
              ),
            );
            try {
              await chatMessgaeCubit.endChat(chatId: chatData!.meetingRequest.chatID, reason: closingChatMessage);
              Navigator.pop(context); 
              ProfileBloc.get(context).getMyProfile();
            } catch (e) {
              Navigator.pop(context); 
              context.getSnackBar(
                snackText: 'حدث خطأ أثناء إنهاء المحادثة',
                isError: true,
              );
            }
        }
      } catch (e) {
        Navigator.pop(context);
        setState(() {
          chatData!.meetingRequest.meetingRequestStatusID = 1;
        });
        chatData!.meetingRequest.receiverAnswerDate = null;
        if (e is DioException) {
          final responseData = e.response?.data;
          context.getSnackBar(
            snackText: responseData['message'],
            isError: true,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          BlocBuilder<ChatMessagesCubit, ChatMessagesState>(
            builder: (context, state) {
              if (state is ChatMessagesLoadingState) {
                return const Center(child: CircularProgressIndicator());
              } else if (state is ChatMessagesErrorState) {
                log('Error: ${state.error}');
                return Stack(
                  children: [
                    Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: CustomAppBar(
                            isHeartTitle: true,
                            isBack: true,
                            isSettingIcon: true,
                            leading: GestureDetector(
                              onTap: () {
                                MagicRouter.navigateTo(PartnerDetailsByIdScreen(
                                  userId: widget.receiverId,
                                  userName: widget.receiverName,
                                ));
                              },
                              child: ClipOval(
                                child: Image.network(
                                  widget.receiverProdileImage,
                                  width: 40,
                                  height: 40,
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return Container(
                                      width: 40,
                                      height: 40,
                                      decoration: const BoxDecoration(
                                        color: ColorManager.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.person,
                                        color: Colors.white,
                                        size: 24,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                        const Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              children: [],
                            ),
                          ),
                        ),
                      ],
                    ),
                    _isSending
                        ? const Positioned(
                            left: 0,
                            right: 0,
                            bottom: 0,
                            child: Padding(
                              padding: EdgeInsets.all(15.0),
                              child: LinearProgressIndicator(),
                            ),
                          )
                        : _isRecording
                            ? const SizedBox()
                            : Positioned(
                                left: 0,
                                right: 0,
                                bottom: 0,
                                child: MessageSendBarWidget(
                                  textController: _sendMessgeController,
                                  startRecording: _startRecording,
                                  sendMessage: (message) {
                                    if (message.isEmpty) {
                                    } else {
                                      _sendMessage(message, false);
                                    }
                                  },
                                )),
                    SequentialInstructionsDialogs()
                  ],
                );
              } else if (state is ChatMessagesLoadedState) {
                final chatMessgaeCubit =
                    BlocProvider.of<ChatMessagesCubit>(context);
                chatData = state.chatData;

                final isSubscribed =
                    ProfileBloc.get(context).profileData?.isSubscribed;
                final chatId = state.chatData.messages.first.chatId;
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (isDisplayMeetingRequestForReceiver) {
                    _showMeetingRequestDialog(context, chatMessgaeCubit);
                    isDisplayMeetingRequestForReceiver = false; // Prevent re-showing
                  }
                });
                return Column(
                  children: [
                    Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: CustomAppBar(
                          isHeartTitle: true,
                          isBack: true,
                          isSettingIcon: true,
                          leading: GestureDetector(
                            onTap: () {
                              if (!state.chatData.isUserDeleted) {
                                MagicRouter.navigateTo(
                                  PartnerDetailsByIdScreen(
                                    userId: widget.receiverId,
                                    userName: widget.receiverName,
                                  ),
                                );
                              }
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize
                                  .min, // Ensures the Row takes only necessary space
                              children: [
                                const SizedBox(width: 2),
                                ClipOval(
                                  child: Image.network(
                                    widget.receiverProdileImage,
                                    width: 40,
                                    height: 40,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Container(
                                        width: 40,
                                        height: 40,
                                        decoration: const BoxDecoration(
                                          color: ColorManager.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.person,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomText(
                                        align: TextAlign.start,
                                        text: state.chatData.isUserDeleted
                                            ? 'مستخدم محذوف'
                                            : widget.receiverName.isNotEmpty
                                                ? widget.receiverName
                                                : "Unknown",
                                        color: ColorManager.primaryColor,
                                        textOverFlow: TextOverflow
                                            .ellipsis, // Avoids overflow
                                      ),
                                      if (!state.chatData.isUserDeleted)
                                        OnlineStatusWidget(
                                          userId: widget.receiverId,
                                          dotSize: 6.0,
                                          showLabel: true,
                                          labelText: SignalRService.isUserOnline(widget.receiverId) 
                                              ? 'متصل' 
                                              : 'غير متصل',
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 8.0, horizontal: 16.0),
                      color: Colors.white,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: (chatData!.isCanSendMeetingRequest && !_isMeetingRequestPending)
                                      ? () =>
                                          _showMeetingRequestConfirmationDialog(context, chatMessgaeCubit, chatId)
                                      : null,
                                  icon: const Icon(
                                    Icons.event,
                                    size: 22,
                                    color: ColorManager.primaryColor,
                                  ),
                                  label: const Text(
                                    'طلب لقاء',
                                    style: TextStyle(
                                      color: ColorManager.primaryColor,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10), // Increase height
                                    backgroundColor: Colors.white,
                                    side: const BorderSide(
                                        color: ColorManager.primaryColor),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(
                                  width: 10), // Space between buttons
                              Expanded(
                                // Ensure both buttons take the same width
                                child: ElevatedButton.icon(
                                  onPressed: chatData!.messages.first.isChatEnded == 1 
                                            || chatData!.meetingRequest.meetingRequestStatusID == 3 ?
                                    null : () => _endChat(chatMessgaeCubit, chatId),
                                  icon: const Icon(
                                    Icons.block,
                                    size: 22,
                                    color: ColorManager.primaryColor,
                                  ),
                                  label: const Text(
                                    'إنهاء المحادثة',
                                    style: TextStyle(
                                      color: ColorManager.primaryColor,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 10), // Increase height
                                    backgroundColor: Colors.white,
                                    side: const BorderSide(
                                        color: ColorManager.primaryColor),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Divider(
                            // Full-width separator line
                            color: ColorManager.primaryColor,
                            thickness: 1.5,
                          ),
                        ],
                      ),
                    ),
                    state.chatData.messages.first.daysToEnd != -1 && 
                    state.chatData.messages.first.daysToEnd >= 1 &&
                            state.chatData.messages.first.daysToEnd <= 3
                        ? FloatingWidget(
                            verticalSpace: 10,
                            duration: const Duration(
                              seconds: 3,
                            ),
                            reverseDuration: const Duration(seconds: 1),
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 5),
                              width: context.width,
                              height: context.height * 0.05,
                              decoration: BoxDecoration(
                                  color: ColorManager.primaryColor,
                                  borderRadius: BorderRadius.circular(35)),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CustomText(
                                    text:
                                        'تنبيه : مدة الدردشة سوف تنتهي في خلال ${state.chatData.messages.first.daysToEnd} أيام',
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return ReasonAlertDialog(
                                            title: 'طلب تمديد وقت المحادثة',
                                            onSubmit: (String reason) async {
                                              log(reason);
                                              String? message =
                                                  await chatMessgaeCubit
                                                      .extensionChatRequest(
                                                chatId: chatId,
                                                reason: reason,
                                              );
                                              Navigator.of(context).pop();
                                              context.getSnackBar(
                                                  snackText: message!,
                                                  isError: message ==
                                                          'ارسلت بالفعل طلب لمد هذه المحادثة'
                                                      ? true
                                                      : false);
                                            },
                                          );
                                        },
                                      );
                                    },
                                    child: const CustomText(
                                      text: 'مدد المدة الأن',
                                      color: Colors.white,
                                      textDecoration: TextDecoration.underline,
                                      decorationColor: Colors.white,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox(),
                    if (showArrowIcon)
                      GestureDetector(
                        onTap: () {
                          if (_scrollController.hasClients) {
                            _scrollController.animateTo(
                              _scrollController.position.minScrollExtent,
                              duration: const Duration(milliseconds: 500),
                              curve: Curves.easeInOut,
                            );
                          }
                        },
                        child: const Icon(Icons.arrow_downward,
                            color: ColorManager.primaryColor),
                      ),
                    if (isLoadingMore)
                      const Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.0),
                        child: LinearProgressIndicator(),
                      ),
                    NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification scrollInfo) {
                        // Check if the user has scrolled to the top of the list
                        if (scrollInfo.metrics.pixels ==
                            scrollInfo.metrics.minScrollExtent) {
                          setState(() {
                            showArrowIcon = false;
                            // currentPage = 1;
                          });
                        } else if (scrollInfo.metrics.pixels ==
                            scrollInfo.metrics.maxScrollExtent) {
                          fetchMoreMessages(widget.receiverId);
                        } else {
                          setState(() {
                            showArrowIcon = true;
                          });
                        }
                        return true;
                      },
                      child: Expanded(
                        child: SingleChildScrollView(
                          controller: _scrollController,
                          reverse: true,
                          child: ListView.builder(
                            reverse: true,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: chatData!.messages.length,
                            itemBuilder: (context, index) {
                              final currentMessage = chatData!.messages[index];
                              final previousMessage = index > 0
                                  ? chatData!.messages[index - 1]
                                  : null;
                              final bool isFirstMessageOfDay =
                                  previousMessage == null ||
                                      !chatMessgaeCubit.isSameDay(
                                          currentMessage.sentAt,
                                          previousMessage.sentAt);

                              return Stack(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      if (isFirstMessageOfDay)
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16.0, vertical: 8.0),
                                          child: Text(
                                            DateFormat(
                                                    'dd/MM/yyyy '
                                                    'الساعة'
                                                    ' hh:mm a',
                                                    window.locale.languageCode)
                                                .format(currentMessage.sentAt),
                                            style: const TextStyle(
                                              color: Colors.grey,
                                              fontSize: 15.0,
                                            ),
                                          ),
                                        ),
                                      if (widget.receiverId !=
                                          currentMessage.recipientId)
                                        AbsorbPointer(
                                          absorbing:
                                              currentMessage.isChatEnded == 1
                                                  ? true
                                                  : false,
                                          child: RecieverBubble(
                                            message: currentMessage.content,
                                            imagePath:
                                                widget.receiverProdileImage,
                                            isGiphy:
                                                chatMessgaeCubit.isGiphyLink(
                                                    currentMessage.content),
                                            isVideo:
                                                chatMessgaeCubit.isVideoLink(
                                                    currentMessage.content),
                                            receiverId: widget.receiverId,
                                            receiverName: widget.receiverName,
                                            messageTime: currentMessage.sentAt,
                                            isActive: currentMessage.isActive,
                                            isUserDeleted:
                                                state.chatData.isUserDeleted,
                                          ),
                                        )
                                      else
                                        GestureDetector(
                                          onLongPress: () {
                                            final currentTime =
                                                DateTime.now().toLocal();
                                            final messageTime =
                                                currentMessage.sentAt.toLocal();
                                            final timeDifference = currentTime
                                                .difference(messageTime);
                                            log('==============> timeDifference inMinutes ${timeDifference.inMinutes}');
                                            if (timeDifference.inMinutes < 10 &&
                                                currentMessage.isActive ==
                                                    true) {
                                              showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return AlertDialog(
                                                    content: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        ListTile(
                                                          title: const Text(
                                                              'مسح الرسالة'),
                                                          onTap: () {
                                                            Navigator.pop(
                                                                context,
                                                                'delete');
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ).then((value) {
                                                if (value == 'delete') {
                                                  context
                                                      .read<ChatMessagesCubit>()
                                                      .deleteMessage(
                                                          currentMessage
                                                              .messageId);
                                                }
                                              });
                                            }
                                          },
                                          child: AbsorbPointer(
                                            absorbing:
                                                currentMessage.isChatEnded == 1
                                                    ? true
                                                    : false,
                                            child: SenderBubble(
                                              isActive: currentMessage.isActive,
                                              message: currentMessage.content,
                                              isGiphy:
                                                  chatMessgaeCubit.isGiphyLink(
                                                      currentMessage.content),
                                              isVideo:
                                                  chatMessgaeCubit.isVideoLink(
                                                      currentMessage.content),
                                              messageTime:
                                                  currentMessage.sentAt,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                    _isSending
                        ? const Padding(
                            padding: EdgeInsets.all(15.0),
                            child: LinearProgressIndicator(),
                          )
                        : _isRecording
                            ? const SizedBox()
                            : (isSubscribed == false)
                                ? const Padding(
                                    padding: EdgeInsets.only(bottom: 8.0),
                                    child: CustomText(
                                      text:
                                          'يرجى الاشتراك في الرزمة الذهبية للرد على الرسائل',
                                      align: TextAlign.center,
                                    ),
                                  )
                                : chatData!.isUserDeleted == true
                                    ? const Padding(
                                        padding: EdgeInsets.only(bottom: 8.0),
                                        child: Center(
                                          child: CustomText(
                                            text:
                                                'لقد تم حذف الحساب لا يمكنك ارسال أو استقبال رسائل',
                                            align: TextAlign.center,
                                          ),
                                        ),
                                      )
                                    : chatData!.messages.first.isChatEnded == 1 || chatData!.meetingRequest.meetingRequestStatusID == 3
                                        ? Padding(
                                            padding:
                                                const EdgeInsets.only(bottom: 8.0),
                                            child: Center(
                                              child: CustomText(
                                                text: chatData!.messages.first.isChatEnded == 1 ? 'تم إنهاء المحادثة' : closingChatMessage,
                                                align: TextAlign.center,
                                              ),
                                            ))
                                        : MessageSendBarWidget(
                                            textController:
                                                _sendMessgeController,
                                            startRecording: _startRecording,
                                            sendMessage: (message) {
                                              if (message.isEmpty) {
                                              } else {
                                                _sendMessage(message, false);
                                              }
                                            },
                                          )
                  ],
                );
              } else if (state is MessageDeleted) {
                return const LoadingCircle();
              } else {
                return const Center(child: Text('! حدث خطأ '));
              }
            },
          ),
          _isRecording
              ? Positioned.fill(
                  child: Container(
                    height: context.height,
                    width: context.width,
                    color: ColorManager.primaryColor.withOpacity(0.5),
                    child: Center(
                      child: ClipOval(
                        child: SizedBox(
                          height: 380,
                          width: 350,
                          child: CameraPreview(_cameraController),
                        ),
                      ),
                    ),
                  ),
                )
              : const SizedBox(),
          _isRecording
              ? Positioned(
                  bottom: 100.0,
                  left: 110.0,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          IconButton(
                            icon: Container(
                                height: 50,
                                width: 50,
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: ColorManager.primaryTextColor),
                                child: const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 40,
                                )),
                            onPressed:
                                _isRecording ? _stopRecording : _startRecording,
                          ),
                          IconButton(
                            icon: Container(
                                height: 50,
                                width: 50,
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: ColorManager.primaryTextColor),
                                child: const Icon(
                                  Icons.rotate_left,
                                  color: Colors.white,
                                  size: 40,
                                )),
                            onPressed:
                                _isSwitchingCamera ? null : _switchCamera,
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              : _videoFile != null &&
                      _isSwitchingCamera == false &&
                      _isSending == false
                  ? Positioned.fill(
                      child: Container(
                        height: context.height,
                        width: context.width,
                        color: ColorManager.primaryColor.withOpacity(0.5),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 300,
                                width: 200,
                                child: VideoPlayerWidget(file: _videoFile!),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  IconButton(
                                    icon: Container(
                                        height: 50,
                                        width: 50,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color:
                                                ColorManager.primaryTextColor),
                                        child: const Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 40,
                                        )),
                                    onPressed: () async {
                                      if (_videoFile != null) {
                                        await _sendVideo(_videoFile!);
                                      } else {
                                        log("No video file available to send");
                                      }
                                    },
                                  ),
                                  IconButton(
                                    icon: Container(
                                        height: 50,
                                        width: 50,
                                        decoration: const BoxDecoration(
                                            shape: BoxShape.circle,
                                            color: Colors.red),
                                        child: const Icon(
                                          Icons.close,
                                          color: Colors.white,
                                          size: 40,
                                        )),
                                    onPressed: _discardVideo,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
          _isRecording
              ? Positioned(
                  bottom: 0,
                  right: 0,
                  left: 0,
                  child: VideoActionSliderWidget(
                    actionSliderController: actionSliderController,
                    stopVideoRecording: () async {
                      await _cameraController.stopVideoRecording();
                      _cameraController.stopVideoRecording();
                      _discardVideo();
                    },
                    onRecordingStopped: () {
                      setState(() {
                        _isRecording = false;
                      });
                    },
                  ))
              : const SizedBox(),
        ],
      ),
    );
  }
}

class VideoPlayerWidget extends StatefulWidget {
  final XFile file;

  const VideoPlayerWidget({Key? key, required this.file}) : super(key: key);

  @override
  _VideoPlayerWidgetState createState() => _VideoPlayerWidgetState();
}

class _VideoPlayerWidgetState extends State<VideoPlayerWidget> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;
  bool _isEnded = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.file(File(widget.file.path))
      ..initialize().then((_) {
        setState(() {});
      });

    _controller.addListener(() {
      if (_controller.value.position == _controller.value.duration) {
        setState(() {
          _isPlaying = false;
          _isEnded = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _controller.value.isInitialized
        ? AspectRatio(
            aspectRatio: _controller.value.aspectRatio,
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                VideoPlayer(_controller),
                VideoProgressIndicator(
                  _controller,
                  allowScrubbing: true,
                  colors: const VideoProgressColors(
                      playedColor: ColorManager.primaryColor),
                ),
                IconButton(
                  icon: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 50.0,
                  ),
                  onPressed: () {
                    setState(() {
                      _isPlaying ? _controller.pause() : _controller.play();
                      _isPlaying = !_isPlaying;
                    });
                  },
                ),
              ],
            ),
          )
        : const Center(child: LoadingCircle());
  }
}
