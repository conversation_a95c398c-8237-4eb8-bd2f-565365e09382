import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class RTLBubble extends StatelessWidget {
  final String text;
  final Color color;
  final TextStyle textStyle;
  final bool isSender;

  const RTLBubble({
    Key? key,
    required this.text,
    required this.color,
    required this.textStyle,
    this.isSender = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use IntrinsicWidth to make the bubble adapt to text length
    return Align(
      alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.7,
        ),
        child: IntrinsicWidth(
          child: Container(
            margin: EdgeInsets.only(
              left: isSender ? 0 : 10, 
              right: isSender ? 10 : 0,
            ),
            child: CustomPaint(
              painter: BubblePainter(
                color: color,
                alignment: isSender ? Alignment.topRight : Alignment.topLeft,
              ),
              child: Container(
                padding: const EdgeInsets.all(12),
                child: Text(
                  text,
                  style: textStyle,
                  textAlign: TextAlign.right,
                  textDirection: ui.TextDirection.rtl,
                  softWrap: true,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class BubblePainter extends CustomPainter {
  final Color color;
  final Alignment alignment;

  BubblePainter({required this.color, required this.alignment});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final Path path = Path();
    const double radius = 12;
    
    if (alignment == Alignment.topRight) {
      // User's message (right side)
      path.addRRect(RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, size.width, size.height),
        topLeft: const Radius.circular(radius),
        topRight: const Radius.circular(radius),
        bottomLeft: const Radius.circular(radius),
      ));
      
      // Add tail on right side
      final tailPath = Path();
      tailPath.moveTo(size.width, size.height / 1.5);
      tailPath.lineTo(size.width + 10, size.height / 1.2);
      tailPath.lineTo(size.width, size.height / 1.1);
      tailPath.close();
      path.addPath(tailPath, Offset.zero);
    } else {
      // Sender's message (left side)
      path.addRRect(RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, size.width, size.height),
        topLeft: const Radius.circular(radius),
        topRight: const Radius.circular(radius),
        bottomRight: const Radius.circular(radius),
      ));
      
      // Add tail on left side
      final tailPath = Path();
      tailPath.moveTo(0, size.height / 1.5);
      tailPath.lineTo(-10, size.height / 1.2);
      tailPath.lineTo(0, size.height / 1.1);
      tailPath.close();
      path.addPath(tailPath, Offset.zero);
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 