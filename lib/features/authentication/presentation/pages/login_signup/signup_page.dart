import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/dimensions.dart';
import 'package:zawaj/core/constants/image_manager.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/bottom_sheet.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/extensions/snack_bar.dart';
import 'package:zawaj/core/helper/cache_helper.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_button.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/features/authentication/presentation/bloc/auth_bloc.dart';
import 'package:zawaj/features/authentication/presentation/bloc/auth_event.dart';
import 'package:zawaj/features/authentication/presentation/pages/login_signup/login_page.dart';
import 'package:zawaj/features/authentication/presentation/pages/otp/email_verify_otp.dart';
import 'package:zawaj/features/setup_account/presentation/pages/gender_screen.dart';
import '../../../../../core/validator/validator.dart';
import '../../../../../core/widgets/custom_text_field.dart';
import '../../../../../core/widgets/loading_circle.dart';
import '../../bloc/auth_states.dart';
import '../../widgets/facebook_login_btn.dart';
import '../../widgets/google_login_btn.dart';
import '../../widgets/apple_login_btn.dart';
part 'units/signup_buttons.dart';
part 'units/signup_forms.dart';

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});
  static final GlobalKey<FormState> signupFormKey = GlobalKey<FormState>();

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  @override
  void initState() {
    initNotification();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return CustomScaffold(
        child: SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(Dimensions.defaultPadding),
        child: Column(
          children: [
            const CustomAppBar(
              isLogoTitle: true,
              isBack: false,
            ),
            const SizedBox(
              height: 15,
            ),
            const CustomText(
              text: Strings.welcome,
              color: ColorManager.greyTextColor,
              //   fontSize: Dimensions.largeFont,
            ),
            const SizedBox(
              height: 10,
            ),
            const CustomText(
              fontWeight: FontWeight.bold,
              text: Strings.create_account,
              // fontSize: Dimensions.smallFont,
            ),
            const SocialMediaSignupButtons(),
            const Row(
              children: [
                Expanded(
                    child: Divider(
                  color: ColorManager.secondaryPinkColor,
                )),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: CustomText(text: Strings.or),
                ),
                Expanded(
                    child: Divider(
                  color: ColorManager.secondaryPinkColor,
                )),
              ],
            ),
            Form(key: SignUpPage.signupFormKey, child: const SignUpForms()),
            BlocConsumer<AuthBloc, AuthStates>(
              listener: (BuildContext context, AuthStates state) {
                if (state is RegisterSuccess) {
                  MagicRouter.navigateAndReplacement(const EmailVerify());
                } else if (state is AccountAlreadyExists) {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: const Text('تنبيه'),
                        content: const Text('هذا الحساب مستخدم بالفعل هل تود الدخول بهذا الحساب'),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: const Text('لا'),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              // Navigate to login page with the email pre-filled
                              // The email is already in the AuthBloc's emailController
                              MagicRouter.navigateAndReplacement(const LoginPage());
                            },
                            child: const Text('نعم'),
                          ),
                        ],
                      );
                    },
                  );
                }
              },
              builder: (BuildContext context, AuthStates state) => state
                      is LoadingAuth
                  ? const LoadingCircle()
                  : CustomButton(
                      onTap: () {
                        if (SignUpPage.signupFormKey.currentState!.validate()) {
                          String? deviceId =
                              CacheHelper.getData(key: Strings.DEVICEID);
                          if (deviceId == null ||
                              deviceId == '' ||
                              deviceId == 'fake') {
                            initNotification();
                          }
                          AuthBloc.get(context).add(RegisterEvent());
                        }
                      },
                      text: Strings.register),
            ),
            const SizedBox(
              height: 10,
            ),
            const SignUpButtons()
          ],
        ),
      ),
    ));
  }
}

initNotification() async {
  try {
    
    String? pushSubscriptionId = OneSignal.User.pushSubscription.id;
    String? onesignalId = await OneSignal.User.getOnesignalId();

    // Use OneSignal ID as fallback if push subscription ID is empty
    String deviceId = pushSubscriptionId?.isNotEmpty == true 
        ? pushSubscriptionId! 
        : (onesignalId?.isNotEmpty == true 
            ? onesignalId! 
            : 'temp_${DateTime.now().millisecondsSinceEpoch}');

    CacheHelper.setData(key: Strings.DEVICEID, value: deviceId);

  } catch (e) {
    // Fallback device ID in case of error
    String fallbackDeviceId = 'fallback_${DateTime.now().millisecondsSinceEpoch}';
    CacheHelper.setData(key: Strings.DEVICEID, value: fallbackDeviceId);
  }

  OneSignal.User.pushSubscription.addObserver((state) {
    String? newPushId = OneSignal.User.pushSubscription.id;
    if (newPushId?.isNotEmpty == true) {
      CacheHelper.setData(key: Strings.DEVICEID, value: newPushId!);
    }
  });

  // Final validation
  String? finalDeviceId = await CacheHelper.getData(key: Strings.DEVICEID);
  
  // Ensure we always have a valid device ID
  if (finalDeviceId == null || finalDeviceId.isEmpty || finalDeviceId == 'fake') {
    String emergencyDeviceId = 'emergency_${DateTime.now().millisecondsSinceEpoch}';
    await CacheHelper.setData(key: Strings.DEVICEID, value: emergencyDeviceId);
  }
}
