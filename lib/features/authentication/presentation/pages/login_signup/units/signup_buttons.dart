part of '../signup_page.dart';

class SignUpButtons extends StatelessWidget {
  const SignUpButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CustomText(
                text: Strings.have_account,
                fontSize: Dimensions.normalFont,
              ),
              const SizedBox(
                width: 10,
              ),
              InkWell(
                  onTap: () {
                    MagicRouter.navigateAndReplacement(const LoginPage());
                  },
                  child: const CustomText(
                    text: Strings.login,
                    fontSize: Dimensions.normalFont,
                    textDecoration: TextDecoration.underline,
                  )),
            ],
          ),
          onTap: () {
            MagicRouter.navigateAndReplacement(const LoginPage());
          },
        ),
      ],
    );
  }
}

class SocialMediaSignupButtons extends StatelessWidget {
  const SocialMediaSignupButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 10,
        ),
        const GoogleLogin(),
        const SizedBox(
          height: 2,
        ),
        const FaceBookLogin(),
        const SizedBox(
          height: 2,
        ),
        AppleLogin(), 
        const SizedBox(
          height: 5
        ),
      ],
    );
  }
}
