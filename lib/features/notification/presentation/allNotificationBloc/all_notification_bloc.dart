import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/features/notification/data/repository/whoLike_repo.dart';
import 'package:zawaj/features/notification/presentation/whoLikedMe_bloc/who_like_me_bloc.dart';
import 'package:zawaj/features/notification/data/models/notificationModel.dart';

class AllNotificationBloc extends Bloc<WhoLikeMeEvent, WhoLikeMeState> {
  static AllNotificationBloc get(context) => BlocProvider.of(context);
  WhoLikedMeRepositoryImp whoLikedMeRepositoryImp;
  List<NotificationModel> _notifications = [];
  int _lastPage = 1;
  bool _hasMore = true;
  AllNotificationBloc({required this.whoLikedMeRepositoryImp})
      : super(WhoLikeMeInitial()) {
    on<AllNotificationEvent>((event, emit) async {
      if (event.page == 1) {
        _notifications = [];
        _hasMore = true;
      }
      emit(AllNotificationLoading());
      var response = await whoLikedMeRepositoryImp.getNotification(page: event.page, limit: event.limit);
      response.fold((failure) {
        emit(AllNotificationFailure(mesaage: failure));
      }, (notificationModel) {
        if (event.page == 1) {
          _notifications = notificationModel;
        } else {
          _notifications.addAll(notificationModel);
        }
        _hasMore = notificationModel.length == event.limit;
        _lastPage = event.page;
        emit(AllNotificationSuccess(List.from(_notifications)));
      });
    });
  }
  bool get hasMore => _hasMore;
  int get lastPage => _lastPage;
}
