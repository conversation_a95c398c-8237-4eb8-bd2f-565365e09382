import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/dimensions.dart';
import 'package:zawaj/core/constants/end_points.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/helper/profile_helper.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_button.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/core/widgets/loading_circle.dart';
import 'package:zawaj/features/home/<USER>/models/home_model.dart';
import 'package:zawaj/features/notification/data/models/notificationModel.dart';
import 'package:zawaj/features/notification/presentation/allNotificationBloc/all_notification_bloc.dart';
import 'package:zawaj/features/notification/presentation/screens/empty_notification.dart';
import 'package:zawaj/features/notification/presentation/screens/likes_notification_page.dart';
import 'package:zawaj/features/notification/presentation/whoLikedMe_bloc/who_like_me_bloc.dart';
import 'package:zawaj/features/notification/presentation/widgets/notificationBodyWidget.dart';
import 'package:zawaj/features/payment/presentation/pages/choose_bundle_screen.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';

class BlurNotification extends StatefulWidget {
  const BlurNotification({super.key});

  @override
  State<BlurNotification> createState() => _BlurNotificationState();
}

class _BlurNotificationState extends State<BlurNotification> {
  final ScrollController _scrollController = ScrollController();

  /// =============== PAGING STATE ===============
  final int _pageLimit = 10;
  int _currentPage = 1;
  bool _isFetchingNextPage = false;
  bool _initialLoaded = false;
  final List<NotificationModel> _notifications = [];
  /// ============================================

  @override
  void initState() {
    super.initState();

    _scrollController.addListener(_onScroll);

    /// initial load
    _fetchPage(reset: true);

    /// profile / subscription checks
    ProfileBloc.get(context).getMyProfile();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ProfileHelper.checkVerificationAndSubscription(context);
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  /* ---------------------------  PAGINATION  ---------------------------- */
  void _onScroll() {
    // when 100 px above the bottom & still have more
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 100 &&
        !_isFetchingNextPage &&
        AllNotificationBloc.get(context).hasMore) {
      _fetchPage();
    }
  }

  void _fetchPage({bool reset = false}) {
    if (reset) {
      _currentPage = 1;
      _notifications.clear();
      _initialLoaded = false;
    } else {
      _currentPage++;
    }

    _isFetchingNextPage = true;
    context
        .read<AllNotificationBloc>()
        .add(AllNotificationEvent(page: _currentPage, limit: _pageLimit));
  }

  /* --------------------------------------------------------------------- */

  @override
  Widget build(BuildContext context) {
    final isSubscribed =
        ProfileBloc.get(context).profileData?.isSubscribed ?? false;

    return CustomScaffold(
      isFullScreen: true,
      child: Column(
        children: [
          const CustomAppBar(
            title: Strings.notification,
            isBack: false,
            leading: MenuButton(),
          ),
          Expanded(
            child: BlocConsumer<AllNotificationBloc, WhoLikeMeState>(
              listener: (context, state) {
                if (state is AllNotificationSuccess) {
                  // Only add notifications that don't already exist
                  final incoming = state.notificationModel;
                  for (var newNotification in incoming) {
                    bool alreadyExists = _notifications.any((existing) {
                      return existing.body == newNotification.body &&
                             existing.date == newNotification.date &&
                             existing.time == newNotification.time;
                    });
                    
                    if (!alreadyExists) {
                      _notifications.add(newNotification);
                    }
                  }
                  _isFetchingNextPage = false;
                  _initialLoaded = true;

                  // if bloc says “no more”, stop listening to scroll
                  if (!AllNotificationBloc.get(context).hasMore) {
                    _scrollController.removeListener(_onScroll);
                  }

                  setState(() {}); // rebuilt with the new items
                } else if (state is AllNotificationFailure) {
                  _isFetchingNextPage = false;
                  _initialLoaded = true;
                  setState(() {});
                }
              },
              builder: (context, state) {
                // ---------- first load ----------
                if (!_initialLoaded && state is AllNotificationLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // ---------- error state ----------
                if (state is AllNotificationFailure && _notifications.isEmpty) {
                  return Center(child: Text(state.mesaage));
                }

                // ---------- empty list ----------
                if (_notifications.isEmpty) {
                  return const EmptyNotification();
                }

                // ---------- list with paging ----------
                return Column(
                  children: [
                    if (!isSubscribed) ...[
                      const CustomText(
                        text: Strings.wholikesyou,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 20),
                      CustomButton(
                        onTap: () {
                          MagicRouter.navigateTo(
                            ChooseBundle(
                              userId: ProfileBloc.get(context)
                                  .profileData!
                                  .userId!,
                              isFromProfileScreen: true,
                            ),
                          );
                        },
                        text: Strings.gopaymentbutton,
                        fontSize: 16,
                      ),
                      const SizedBox(height: 10),
                    ],
                    Expanded(
                      child: ListView.builder(
                        controller: _scrollController,
                        itemCount: _notifications.length +
                            (_isFetchingNextPage ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index < _notifications.length) {
                            return NotificationBody(
                              notificationModel: _notifications[index],
                            );
                          }

                          // smooth bottom loader (shows immediately, no flicker)
                          // return const Padding(
                          //   padding: EdgeInsets.symmetric(vertical: 8.0),
                          //   child: Center(
                          //     child: SizedBox(
                          //       width: 32,
                          //       height: 32,
                          //       child: CircularProgressIndicator(
                          //         strokeWidth: 3,
                          //         color: ColorManager.primaryColor,
                          //       ),
                          //     ),
                          //   ),
                          // );
                            return const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8.0),
                                  child: LinearProgressIndicator(
                                    color: ColorManager.primaryColor,
                                    minHeight: 3,        // slim line – tweak if you want it taller
                                  ),
                                );
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
