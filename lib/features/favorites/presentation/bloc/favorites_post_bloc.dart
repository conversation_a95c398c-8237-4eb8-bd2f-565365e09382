import 'dart:developer';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/features/favorites/data/repository/favorites_repo.dart';
import 'package:zawaj/features/home/<USER>/models/home_model.dart';

part 'favorites_post_event.dart';
part 'favorites_post_state.dart';

class LikedPartnersBloc extends Bloc<LikedPartnersEvent, LikedPartnersStates> {
  static LikedPartnersBloc get(context) => BlocProvider.of(context);
  LikedPartnersRepositoryImp likedPartnersRepositoryImp;

  List<HomeModel> homeModel = [];
  int currentPage = 1;
  bool isLoadingMore = false;

  LikedPartnersBloc({required this.likedPartnersRepositoryImp})
      : super(LikedPartnersInitial()) {
    on<LoadLikedPartnersEvent>((event, emit) async {
      emit(LikedPartnersLoading());

      if (isLoadingMore) return;

      isLoadingMore = true;

      var response =
          event.type == 'I liked' ? await likedPartnersRepositoryImp.likedPartners(page: event.page) : await likedPartnersRepositoryImp.whoLikedMe(page: event.page);
      response.fold((failure) {
        emit(LikedPartnersFailed(message: failure));
        isLoadingMore = false;
      }, (fetchedModels) {
        if (event.isReset == true) {
          homeModel = [];
        }
        for (var model in fetchedModels) {
          if (!homeModel.any((existing) => existing.userId == model.userId)) {
            homeModel.add(model);
          }
        }
        emit(LikedPartnersSuccess(homeModel));
        isLoadingMore = false;
      });
    });
  }

  void loadMore() {
    currentPage++;
    add(LoadLikedPartnersEvent(currentPage));
  }

  void loadPrevious() {
    if (currentPage > 1) {
      currentPage--;
      add(LoadLikedPartnersEvent(currentPage));
    }
  }
}
