import 'package:bloc/bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/features/home/<USER>/models/home_model.dart';
import 'package:zawaj/features/home/<USER>/repository/home_repo.dart';

part 'home_event.dart';
part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeRepositoryImp homeRepositoryImp;
  static HomeBloc get(context) => BlocProvider.of(context);

  HomeBloc({required this.homeRepositoryImp}) : super(HomeInitial()) {}
  HomeModel? homeModel;
  List<HomeModel> homeModelList = [];
  int currentPage = 1;
  getHomeData() async {
    emit(HomeLoading());
    currentPage = 1; // Reset to page 1 for initial load
    homeModelList.clear(); // Clear existing data
    var response = await homeRepositoryImp.getPartnerData(page: currentPage);
    print(response);
    print("Calling <HomeBloc>");
    response.fold((failure) {
      emit(HomeFailure(message: failure));
      print("Emit : (HomeFailure)");
    }, (homeList) {
      homeModelList = homeList;
      if (homeList.isNotEmpty) {
        currentPage++;
      }
      emit(HomeSuccess(homeList));
      print("Emit : (HomeSuccess)");
    });
  }

  //////////////////for pagination///////////////////
  Future<void> fetchNextPage() async {
    emit(HomeLoading());
    var response = await homeRepositoryImp.getPartnerData(page: currentPage);
    response.fold(
      (failure) => emit(HomeFailure(message: failure)),
      (homeList) {
        // Add duplicate checking to prevent duplicate items
        for (var newItem in homeList) {
          if (!homeModelList.any((existingItem) => existingItem.id == newItem.id)) {
            homeModelList.add(newItem);
          }
        }
        if (homeList.isNotEmpty) {
          currentPage++;
        }
        emit(HomeSuccess(homeModelList));
      },
    );
  }
}
