import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/dimensions.dart';
import 'package:zawaj/core/constants/end_points.dart';
import 'package:zawaj/core/constants/image_manager.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/extensions/snack_bar.dart';
import 'package:zawaj/core/helper/cache_helper.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/build_dialog.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/core/widgets/online_status_widget.dart';
import 'package:zawaj/features/chat/data/data_source/chat_messages_dataSource.dart';
import 'package:zawaj/features/chat/data/repos/chat_message_repository.dart';
import 'package:zawaj/features/chat/domain/get_chat_messages_usecase.dart';
import 'package:zawaj/features/chat/presentation/chat_message_cubit/chat_messages_cubit.dart';
import 'package:zawaj/features/chat/presentation/screens/chat_screen.dart';
import 'package:zawaj/features/favorites/presentation/bloc/favorites_post_bloc.dart';
import 'package:zawaj/features/home/<USER>/models/home_model.dart';
import 'package:zawaj/features/home/<USER>/pages/partner_details_screen.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';

import '../blocs/home_bloc/home_bloc.dart';
import '../blocs/likedPost_bloc/liked_post_bloc.dart';

// Add this custom donut progress widget
class DonutProgressIndicator extends StatelessWidget {
  final double progress;
  final double size;
  final double strokeWidth;

  const DonutProgressIndicator({
    Key? key,
    required this.progress,
    this.size = 40,
    this.strokeWidth = 4,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final normalizedProgress = (progress / 100).clamp(0.0, 1.0);
    final color = progress < 50 ? Colors.red : Colors.green;
    
    return SizedBox(
      width: size,
      height: size,
      child: Stack(
        children: [
          // Background circle
          CircularProgressIndicator(
            value: 1.0,
            strokeWidth: strokeWidth,
            backgroundColor: Colors.grey.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.withOpacity(0.3)),
          ),
          // Progress circle
          CircularProgressIndicator(
            value: normalizedProgress,
            strokeWidth: strokeWidth,
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
          // Center text
          Center(
            child: Text(
              '${progress.toInt()}%',
              style: TextStyle(
                fontSize: size * 0.25,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool isLoading = false;
  bool isInitialLoad = true;
  String today = DateFormat('MM/dd/yyyy').format(DateTime.now());
  @override
  void initState() {
    super.initState();
    ProfileBloc.get(context).getMyProfile();
    LikedPartnersBloc.get(context)
        .add(LoadLikedPartnersEvent(1, isReset: true));
    
    // Always load fresh data when visiting homepage
    loadHomeData(reset: true);
    isInitialLoad = false;
  }

  Future<void> loadHomeData({bool reset = false}) async {
    if (!isLoading) {
      setState(() => isLoading = true);

      if (reset) {
        await HomeBloc.get(context).getHomeData();
      } else {
        await HomeBloc.get(context).fetchNextPage();
      }
      
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeBloc, HomeState>(
      listener: (context, state) {},
      builder: (context, state) {
        // Show center loading only when homeModelList is empty (initial load)
        if (state is HomeLoading && HomeBloc.get(context).homeModelList.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Card(
                  surfaceTintColor: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CupertinoActivityIndicator(
                          color: ColorManager.primaryColor,
                        ),
                        SizedBox(height: 20),
                        Text(
                          'تحميل ...',
                          style:
                              TextStyle(color: ColorManager.primaryTextColor),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        } else {
          return BlocConsumer<LikedPostBloc, HomeState>(
            listener: (context, state) {
              if (state is IsLikedPostFailure) {
                context.getSnackBar(snackText: state.message, isError: true);
              }
              if (state is IsLikedPostSuccess) {
                context.getSnackBar(
                  snackText: state.message,
                );
              }

              if (state is RemoveUserFailure) {
                context.getSnackBar(snackText: state.message, isError: true);
              }

              if (state is RemoveUserSuccess) {
                context.getSnackBar(
                  snackText: state.message,
                );
              }
            },
            builder: (context, state) {
              return NotificationListener<ScrollNotification>(
                onNotification: (ScrollNotification scrollInfo) {
                  if (!isLoading &&
                      scrollInfo.metrics.pixels ==
                          scrollInfo.metrics.maxScrollExtent) {
                    loadHomeData();
                  }
                  return true;
                },
                child: CustomScaffold(
                  isFullScreen: true,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const CustomAppBar(
                        isSettingIcon: true,
                        isBack: false,
                        isHeartTitle: true,
                        leading: MenuButton(),
                        //  GestureDetector(
                        //   onTap: () {
                        //     showPopover(
                        //         context: context,
                        //         bodyBuilder: (context) =>
                        //             const OtherEditsPopUp(),
                        //         backgroundColor: Colors.transparent,
                        //         radius: 25,
                        //         width: 250);
                        //     //  MagicRouter.navigateTo(
                        //     //      SetPartnerData(isUpdated: true));
                        //   },
                        //   child: SvgPicture.asset(
                        //     ImageManager.menuIcon,
                        //     fit: BoxFit.scaleDown,
                        //   ),
                        // ),
                      ),
                      HomeBloc.get(context).homeModelList.isEmpty
                          ? const Expanded(
                              child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                CustomText(
                                  text: Strings.noRequiredyet,
                                ),
                              ],
                            ))
                          : Expanded(
                              child: ListView.builder(
                                itemCount:
                                    HomeBloc.get(context).homeModelList.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return PartnerWidget(
                                    homeModel: HomeBloc.get(context)
                                        .homeModelList[index],
                                    isSubscribed: ProfileBloc.get(context)
                                            .profileData
                                            ?.isSubscribed ??
                                        false,
                                    onRemoveUser: (removedUser) {
                                      setState(() {
                                        HomeBloc.get(context)
                                            .homeModelList
                                            .removeWhere((user) =>
                                                user.userId.toString() ==
                                                removedUser.userId);
                                      });
                                    },
                                  );
                                },
                              ),
                            ),
                      if (isLoading || (state is HomeLoading && HomeBloc.get(context).homeModelList.isNotEmpty))
                        const LinearProgressIndicator(
                          color: ColorManager.primaryColor,
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        }
      },
    );
  }
}

class PartnerWidget extends StatefulWidget {
  PartnerWidget(
      {Key? key,
      required this.homeModel,
      required this.isSubscribed,
      required this.onRemoveUser})
      : super(key: key);
  final HomeModel homeModel;
  final bool isSubscribed;
  final Function(HomeModel) onRemoveUser;

  @override
  _PartnerWidgetState createState() => _PartnerWidgetState();
}

String receiverUserIdChat = CacheHelper.getData(
  key: Strings.receiverUserIdChat,
);

class _PartnerWidgetState extends State<PartnerWidget> {
  int? lastMeetingRequestStatusIDByUser;
  bool? lastMeetingRequestResultByUser;
  String? chatUserID;
  String? theOtherUserIDInMeetingRequest;
  bool isOpenChat = true;


  Future<void> _fetchGetLastMeetingRequestStatusByUser() async {
    try {
      final response = await HomeBloc.get(context)
          .homeRepositoryImp
          .getLastMeetingRequestStatusByUser();
      setState(() {
        lastMeetingRequestStatusIDByUser = response.data['result']['lastMeetingRequestStatusIDByUser'];
        lastMeetingRequestResultByUser = response.data['result']['lastMeetingRequestResultByUser'];
        chatUserID = response.data['result']['chatUserID'];
        theOtherUserIDInMeetingRequest = response.data['result']['theOtherUserIDInMeetingRequest'];
      });
    } catch (e) {
      setState(() {
        isOpenChat = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!isOpenChat) {
      return const Center(child: Text('! حدث خطأ '));
    }
    return BlocConsumer<LikedPostBloc, HomeState>(
      listener: (context, state) {},
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              alignment: AlignmentDirectional.bottomEnd,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: widget.homeModel.images == null ||
                          widget.homeModel.images!.isEmpty
                      ? const Icon(Icons.add)
                      : GestureDetector(
                          onTap: () {
                            MagicRouter.navigateTo(PartnerDetailsScreen(
                                homeModel: widget.homeModel));
                          },
                          child: Image.network(
                              EndPoints.BASE_URL_image +
                                  widget.homeModel.images![0],
                              width: context.width,
                              height: context.height * 0.30,
                              fit: BoxFit.fill,
                              errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.white,
                              width: context.width,
                              // height: context.height * 0.5,
                              height: context.height * 0.30,

                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Center(
                                    child: Image.asset(
                                      ImageManager.profileError,
                                      height: context.height * 0.30,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }, frameBuilder: (context, child, frame,
                                  wasSynchronouslyLoaded) {
                            return child;
                          }, loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) {
                              return child;
                            } else {
                              return Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  width: context.width,
                                  height: context.height * 0.5,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                              );
                            }
                          }),
                        ),
                ),
                // Online status indicator
                if (widget.homeModel.isOnline == true)
                  Positioned(
                    top: 15,
                    right: 15,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Text(
                            'متّصل ',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                // Match score progress bar in bottom right
                if (widget.homeModel.matchScore != null)
                  Positioned(
                    bottom: 15,
                    right: 15,
                    child: Container(
                      width: 60,
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(6),
                        // border: Border.all(
                        //   color: ColorManager.primaryColor,
                        //   width: 0.1,
                        // ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            height: 12,
                            decoration: BoxDecoration(
                              // color: Colors.grey.withOpacity(0.2),

                              color:  Colors.white24, // Option 1
                              // color:  Colors.black12, // Option 3


                              // color:  Colors.black38, // Option 3

                              // color:  Colors.black26, // Option 2
                              // -------
                              // border: Border.all(
                              //   color: ColorManager.primaryColor,
                              //   width: 0.1,
                              // ),
                              // -------
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Stack(
                              children: [
                                // Background container (no dashed lines)
                                Container(
                                  height: 12,
                                  width: double.infinity,
                                ),
                                // Progress fill
                                FractionallySizedBox(
                                  alignment: Alignment.centerLeft,
                                  widthFactor: (widget.homeModel.matchScore! / 100).clamp(0.0, 1.0),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: widget.homeModel.matchScore! < 50 ? Colors.red.shade600 : Colors.green.shade600,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                ),
                                // Percentage text inside the bar
                                Center(
                                  child: Text(
                                    '${widget.homeModel.matchScore!.toInt()}%',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 8,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 3),
                          const Text(
                            'مدى الملاءمة',
                            style: TextStyle(
                              // color: Colors.black, 
                              color: ColorManager.primaryColor, 
                              fontSize: 7,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                Padding(
                  padding: const EdgeInsets.all(15),
                  child: Column(
                    children: [
                      Card(
                        color: Colors.white.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40)),
                        child: GestureDetector(
                          onTap: () {
                            buildDialog(
                                context: context,
                                onTapEnter: () {
                                  // BlocProvider.of<LikedPostBloc>(context).add(
                                  LikedPostBloc.get(context).add(
                                    RemoveUserEvent(
                                        userId: widget.homeModel.id.toString()),
                                  );
                                  widget.onRemoveUser(widget.homeModel);
                                  final likedPartnersBloc =
                                      LikedPartnersBloc.get(context);
                                  // BlocProvider.of<LikedPartnersBloc>(context);
                                  likedPartnersBloc.add(
                                      LoadLikedPartnersEvent(1, isReset: true));
                                  Navigator.pop(context);
                                },
                                buttonTitle: Strings.yes,
                                title: Strings.block,
                                desc: Strings.block_confirm);
                          },
                          child: Container(
                            height: 50,
                            width: 50,
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: ColorManager.primaryColor, width: 1),
                                shape: BoxShape.circle,
                                color: Colors.white.withOpacity(0.5)),
                            child:
                                // block
                                Icon(
                              Icons.block,
                              color: ColorManager.primaryColor,
                            ),
                            // SvgPicture.asset(
                            //   ImageManager.closeIcon,
                            //   color: ColorManager.primaryColor,
                            //   width: 14,
                            //   height: 14,
                            //   fit: BoxFit.scaleDown,
                            // )
                          ),
                        ),
                      ),
                      Card(
                        color: Colors.white.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40)),
                        child: GestureDetector(
                          onTap: () async {
                            await _fetchGetLastMeetingRequestStatusByUser();
                            final isSubscribed = ProfileBloc.get(context)
                                .profileData
                                ?.isSubscribed;
                            if (isSubscribed == true) {
                              if (chatUserID != null && chatUserID != widget.homeModel.userId) {
                                context.getSnackBar(
                                    snackText: 'يجب عليك إنهاء الدردشة الحالية أولا', isError: true);
                              } else if (lastMeetingRequestStatusIDByUser != null 
                                        && (lastMeetingRequestStatusIDByUser == 3 || (lastMeetingRequestStatusIDByUser == 4 && lastMeetingRequestResultByUser == null))
                                        && theOtherUserIDInMeetingRequest != widget.homeModel.userId) {
                                context.getSnackBar(
                                    snackText: 'هنالك طلب مقابلة قيد التنسيق عبر إدارة التطبيق. المحادثة مغلقة.', isError: true);
                              }
                              else if (lastMeetingRequestResultByUser != null && lastMeetingRequestResultByUser == true
                                      && theOtherUserIDInMeetingRequest != widget.homeModel.userId) {
                                context.getSnackBar(
                                    snackText: 'لا يمكنك إجراء محادثات إضافية بسبب تقرير الإدارة عن تقدمك بنجاح مع طرف اخر. الرجاء التوجه لإدارة التطبيق لتفاصيل إضافية.', isError: true);
                              } else {
                                MagicRouter.navigateTo(BlocProvider(
                                  create: (context) => ChatMessagesCubit(
                                      GetChatMessagesDataUseCase(
                                          ChatMessagesRepositoryImpl(
                                              ChatDataProvider())))
                                    ..getChatMessagesData(
                                        widget.homeModel.userId.toString()),
                                  child: ChatScreen(
                                    receiverProdileImage:
                                        EndPoints.BASE_URL_image +
                                            widget.homeModel.images![0],
                                    receiverId: widget.homeModel.userId!,
                                    homeModel: widget.homeModel,
                                    receiverName: widget.homeModel.name!,
                                  ),
                                ));
                              }
                            } else {
                              context.getSnackBar(
                                  snackText:
                                      'لأتاحة امكانية الدردشة, عليك الاشتراك بالرزمة الذهبية',
                                  snackHelperText:
                                      'بأمكانك الدخول الي حسابك عبر الضغط على زر "حسابي" أسفل الشاشة و شراء الرزمة الذهبية',
                                  isError: true);
                            }
                          },
                          child: Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: ColorManager.primaryColor,
                                      width: 1),
                                  shape: BoxShape.circle,
                                  color: Colors.white.withOpacity(0.5)),
                              child: SvgPicture.asset(
                                ImageManager.chatIcon,
                                color: ColorManager.primaryColor,
                                width: 14,
                                height: 14,
                                fit: BoxFit.scaleDown,
                              )),
                        ),
                      ),
                      Card(
                        color: Colors.white.withOpacity(0.1),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40)),
                        child: GestureDetector(
                          onTap: () {
                            if (LikedPostBloc.get(context).isLoading == false) {
                              if (widget.homeModel.isLiked == false) {
                                LikedPostBloc.get(context).add(
                                  LikePostEvent(
                                      userId: widget.homeModel.id.toString()),
                                );

                                setState(() {
                                  widget.homeModel.isLiked = true;
                                });
                              } else {
                                LikedPostBloc.get(context).add(
                                  DisLikePostEvent(
                                      userId: widget.homeModel.id.toString()),
                                );
                                setState(() {
                                  widget.homeModel.isLiked = false;
                                });
                                final likedPartnersBloc =
                                    LikedPartnersBloc.get(context);
                                likedPartnersBloc.add(
                                    LoadLikedPartnersEvent(1, isReset: true));
                                // BlocProvider.of<HomeBloc>(context)
                                //     .getHomeData();
                              }
                            } else {}
                          },
                          child: Container(
                              height: 50,
                              width: 50,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: ColorManager.primaryColor,
                                      width: 1),
                                  shape: BoxShape.circle,
                                  color: Colors.white.withOpacity(0.5)),
                              child: widget.homeModel.isLiked == true
                                  ? const Icon(
                                      Icons.favorite,
                                      color: ColorManager.primaryColor,
                                    )
                                  : SvgPicture.asset(
                                      ImageManager.favIcon,
                                      width: 14,
                                      height: 14,
                                      fit: BoxFit.scaleDown,
                                    )),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // CustomText(
                    //   text: widget.homeModel.age.toString(),
                    //   fontSize: Dimensions.largeFont,
                    //   align: TextAlign.start,
                    //   textOverFlow: TextOverflow.ellipsis,
                    // ),
                    // const SizedBox(
                    //   width: 10,
                    // ),
                    Expanded(
                      child: CustomText(
                        lines: 6,
                        align: TextAlign.start,
                        text:
                            '${widget.homeModel.name}, ${widget.homeModel.age}',
                        fontSize: Dimensions.largeFont,
                      ),
                    ),
                    const SizedBox(
                      width: 15,
                    ),
                    // const Spacer(
                    //   flex: 3,
                    // ),
                    Align(
                      alignment: Alignment.topLeft,
                      child: CustomText(
                        align: TextAlign.left,
                        text: widget.homeModel.city,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                    child: CustomText(
                  text: widget.homeModel.about ?? '',
                  fontSize: Dimensions.normalFont,
                  align: TextAlign.start,
                  lines: 2,
                )),
              ],
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        );
      },
    );
  }
}
