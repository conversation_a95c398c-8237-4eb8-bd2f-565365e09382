import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:popover/popover.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/constants/dimensions.dart';
import 'package:zawaj/core/constants/end_points.dart';
import 'package:zawaj/core/constants/image_manager.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/network/network_info_imp.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/build_dialog.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/core/widgets/loading_circle.dart';
import 'package:zawaj/features/dashboard/view.dart';
import 'package:zawaj/features/favorites/presentation/bloc/favorites_post_bloc.dart';
import 'package:zawaj/features/home/<USER>/blocs/home_bloc/home_bloc.dart';
import 'package:zawaj/features/home/<USER>/blocs/likedPost_bloc/liked_post_bloc.dart';
import 'package:zawaj/features/profile/data/data_source/user_by_id_datasource.dart';
import 'package:zawaj/features/profile/data/models/user_by_id_model.dart';
import 'package:zawaj/features/profile/data/repository/user_by_id_repository.dart';
import 'package:zawaj/features/profile/presentation/bloc/user_by_id_bloc.dart';
import 'package:zawaj/features/profile/presentation/pages/popup_pages/send_report/presentation/screen/send_report.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

class PartnerDetailsByIdScreen extends StatelessWidget {
  final String userId;
  final String userName;

  const PartnerDetailsByIdScreen({
    Key? key,
    required this.userId,
    required this.userName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => UserByIdBloc(
        UserByIdRepository(
          dataSource: UserByIdDataSource(),
          networkInfo: NetworkInfoImp(internetConnectionChecker: InternetConnectionChecker()),
        ),
      )..add(GetUserByIdEvent(userId)),
      child: BlocBuilder<UserByIdBloc, UserByIdState>(
        builder: (context, state) {
          if (state is UserByIdLoading) {
            return const CustomScaffold(
              isFullScreen: true,
              child: Center(
                child: LoadingCircle(),
              ),
            );
          } else if (state is UserByIdError) {
            return CustomScaffold(
              isFullScreen: true,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const CustomAppBar(isHeartTitle: true),
                    Center(
                      child: CustomText(
                        text: state.message,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else if (state is UserByIdLoaded) {
            final user = state.user;
            return CustomScaffold(
              isFullScreen: true,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    CustomAppBar(
                      isHeartTitle: true,
                      leading: MenuReportButton(userId: userId, userName: userName),
                    ),
                    InfoLabel(user: user),
                  ],
                ),
              ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }
}

class MenuReportButton extends StatelessWidget {
  const MenuReportButton({
    super.key,
    required this.userId,
    required this.userName,
  });
  
  final String userId;
  final String userName;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showPopover(
          backgroundColor: Colors.transparent,
          direction: PopoverDirection.top,
          context: context,
          bodyBuilder: (context) => ReportCard(userId: userId, userName: userName),
          radius: 25,
          width: 250,
        );
      },
      child: const Padding(
          padding: EdgeInsets.all(0),
          child: Icon(
              color: ColorManager.primaryColor,
              size: 30,
              Icons.more_horiz_outlined)),
    );
  }
}

class InfoLabel extends StatelessWidget {
  const InfoLabel({super.key, required this.user});

  final UserByIdModel user;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: CarouselSlider(
            items: (user.images ?? []).map((image) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Image.network(
                  EndPoints.BASE_URL_image + image,
                  width: context.width,
                  height: context.height * 0.3,
                  fit: BoxFit.fill,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.white,
                      width: context.width,
                      height: context.height * 0.5,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: Image.asset(
                              ImageManager.profileError,
                              width: 100,
                              height: 100,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                  frameBuilder:
                      (context, child, frame, wasSynchronouslyLoaded) {
                    return child;
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) {
                      return child;
                    } else {
                      return const Center(
                        child: LoadingCircle(),
                      );
                    }
                  },
                ),
              );
            }).toList(),
            options: CarouselOptions(autoPlay: true, enlargeCenterPage: true),
          ),
        ),
        const SizedBox(
          height: 10,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: CustomText(
                    lines: 6,
                    align: TextAlign.start,
                    text: '${user.name ?? ''}, ${user.age ?? ''}',
                    fontSize: Dimensions.largeFont,
                  ),
                ),
                const SizedBox(
                  width: 15,
                ),
                CustomText(
                  text: user.city ?? '',
                ),
              ],
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
                child: CustomText(
              text: user.about ?? '',
              fontSize: Dimensions.normalFont,
              align: TextAlign.start,
            )),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
        Wrap(
          spacing: 25,
          runSpacing: 10,
          children: [
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "العمر"),
                  CustomText(
                    text: user.age?.toString() ?? '',
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  )
                ],
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      align: TextAlign.start,
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "الحالة الاجتماعية"),
                  CustomText(
                    text: _getMaritalStatusText(user.maritalStatus, user.gender),
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  )
                ],
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "المدينة"),
                  CustomText(
                    text: user.area ?? '',
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "الوزن"),
                  CustomText(
                    text: user.weight?.toString() ?? '',
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "الطول"),
                  CustomText(
                    text: user.height?.toString() ?? '',
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width / 3.2 - 30,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomText(
                      color: ColorManager.hintTextColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                      text: "الديانة"),
                  CustomText(
                    text: user.religion == 'Muslim' ? 'مسلم' : user.religion == 'Christian' ? 'مسيحي' : user.religion == 'Darzi' ? 'درزي' : user.religion ?? '',
                    color: ColorManager.primaryTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
            ParametersList(parameters: user.parameters)
          ],
        ),
      ],
    );
  }

  String _getMaritalStatusText(String? maritalStatus, String? gender) {
    if (maritalStatus == null) return '';
    
    switch (maritalStatus) {
      case 'Single':
        return gender == 'Female' ? 'عزباء' : 'اعزب';
      case 'Married':
        return gender == 'Female' ? 'متزوجة' : 'متزوج';
      case 'divorced':
        return 'مطلّق/مطلّقة';
      case 'widowerWithChildern':
        return 'ارملة مع اولاد';
      case 'DivorcedWithChildern':
        return gender == 'Female' ? 'مطلّقة بأطفال' : 'مطلّق بأطفال';
      case 'DivorcedWithoutChildern':
        return gender == 'Female' ? 'مطلّقة بدون أطفال' : 'مطلّق بدون أطفال';
      case 'Widower':
        return gender == 'Female' ? 'أرملة' : 'أرمل';
      case 'Other':
        return 'أخرى';
      default:
        return maritalStatus;
    }
  }
}

class ReportCard extends StatelessWidget {
  const ReportCard({super.key, required this.userId, required this.userName});
  
  final String userId;
  final String userName;
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: MediaQuery.of(context).size.width / 3.2 - 30,
        child: Card(
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadiusDirectional.only(
                  topStart: Radius.circular(Dimensions.buttonRadius),
                  topEnd: Radius.circular(Dimensions.buttonRadius),
                  bottomStart: Radius.circular(Dimensions.buttonRadius))),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                    onTap: () {
                      MagicRouter.navigateTo(SendReport(
                        userId: userId,
                        userName: userName,
                      ));
                    },
                    child: const CustomText(text: Strings.report_user)),
                const Divider(),
                InkWell(
                    onTap: () {
                      buildDialog(
                          context: context,
                          onTapEnter: () {
                            LikedPostBloc.get(context).add(
                              RemoveUserEvent(userId: userId),
                            );
                            final likedPartnersBloc =
                                LikedPartnersBloc.get(context);
                            likedPartnersBloc.add(
                                LoadLikedPartnersEvent(1, isReset: true));
                            MagicRouter.navigateAndPopAll(
                                const DashBoardScreen(initialIndex: 2));
                          },
                          buttonTitle: Strings.yes,
                          title: Strings.block,
                          desc: Strings.block_confirm);
                    },
                    child: const Row(
                      children: [
                        Icon(
                          Icons.block,
                          color: ColorManager.primaryColor,
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        CustomText(text: Strings.block),
                      ],
                    )),
              ],
            ),
          ),
        ));
  }
}

class ParametersList extends StatelessWidget {
  const ParametersList({super.key, required this.parameters});

  final List<ParameterUserDto>? parameters;

  @override
  Widget build(BuildContext context) {
    if (parameters == null || parameters!.isEmpty) {
      return Container();
    }
    return Wrap(
      spacing: 25,
      runSpacing: 10,
      children: parameters!.map((parameter) {
        return SizedBox(
          width: MediaQuery.of(context).size.width / 3.2 - 30,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                align: TextAlign.start,
                color: ColorManager.hintTextColor,
                fontSize: 18,
                fontWeight: FontWeight.w400,
                text: parameter.valueName == 'لا شيء مما سبق'
                    ? ''
                    : parameter.parameterName ?? '',
              ),
              CustomText(
                text: parameter.valueName == 'لا شيء مما سبق'
                    ? ''
                    : parameter.valueName ?? '',
                color: ColorManager.primaryTextColor,
                fontSize: 18,
                fontWeight: FontWeight.w400,
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
} 