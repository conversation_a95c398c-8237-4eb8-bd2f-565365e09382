import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:zawaj/core/constants/image_manager.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/helper/cache_helper.dart';
import 'package:zawaj/core/router/routes.dart';
import 'package:zawaj/core/widgets/custom_appbar.dart';
import 'package:zawaj/core/widgets/custom_button.dart';
import 'package:zawaj/core/widgets/custom_text.dart';
import 'package:zawaj/features/authentication/presentation/pages/login_signup/signup_page.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:zawaj/features/profile/presentation/pages/verification/verify_account_screen.dart';
import 'package:zawaj/features/setup_account/presentation/pages/set_partenal_data.dart';

class YourProfileIsRejected extends StatefulWidget {
  const YourProfileIsRejected({super.key, this.isFromVerification = false});
  
  final bool isFromVerification;

  @override
  State<YourProfileIsRejected> createState() => _YourProfileIsRejectedState();
}

class _YourProfileIsRejectedState extends State<YourProfileIsRejected> {
  @override
  void initState() {
    super.initState();
    // Fetch profile data when page is visited
    ProfileBloc.get(context).getMyProfile();
  }

  @override
  Widget build(BuildContext context) {
    final rejectReason = ProfileBloc.get(context).profileData?.rejecteReason;
    
    return Scaffold(
      //   bottomNavigationBar:
      // Padding(
      //     padding: const EdgeInsets.all(15.0),
      //     child: CustomButton(
      //       text: 'ابدأ البحث عن شريكك الآن',
      //       onTap: () {
      //         MagicRouter.navigateTo(SetPartnerData(
      //           isUpdated: false,
      //         ));
      //       },
      //     ),
      //   ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CustomAppBar(
            isHeartTitle: true,
            isBack: false,
          ),
          // Padding(
          //   padding: EdgeInsets.only(left: context.width * 0.3),
          //   child: Image.asset(
          //     ImageManager.congratulations,
          //     height: 350.0,
          //     width: 350.0,
          //   ),
          // ),
          const CustomText(
            text: 'تم رفض طلبك ',
            fontSize: 25,
          ),
          SizedBox(
            height: context.height * 0.015,
          ),
          const CustomText(
            text: '',
            color: Colors.black,
            fontSize: 20,
          ),
          if (rejectReason != null) ...[
            SizedBox(
              height: context.height * 0.05,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30.0),
              child: Align(
                alignment: Alignment.centerRight,
                child: const CustomText(
                  text: 'السبب',
                  fontSize: 25,
                ),
              ),
            ),
            SizedBox(
              height: context.height * 0.015,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30.0),
              child: Align(
                alignment: Alignment.centerRight,
                child: CustomText(
                  text: rejectReason,
                  color: Colors.black,
                  fontSize: 20,
                ),
              ),
            ),
          ],
          SizedBox(
            height: context.height * 0.3,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30.0),
            child: CustomButton(
              text: 'تعديل التفاصيل',
              onTap: () {
                // Set flag to track that user is coming from verification/rejection
                CacheHelper.setData(key: Strings.isFromVerification, value: true);
                MagicRouter.navigateTo(const VerifyScreen());
              },
            ),
          ),
          SizedBox(
            height: context.height * 0.05,
          ),
          GestureDetector(
            onTap: () async {
              await FirebaseAuth.instance.signOut();
              GoogleSignIn googleSignIn = GoogleSignIn();
              googleSignIn.disconnect();

              // Clear ALL verification and status flags
              await CacheHelper.removeData(key: Strings.token);
              await CacheHelper.removeData(key: Strings.hasSetup);
              await CacheHelper.removeData(key: Strings.hasRequired);
              await CacheHelper.removeData(key: Strings.verificationState);
              await CacheHelper.removeData(key: Strings.isFromVerification);
              await CacheHelper.removeData(key: Strings.isInReVerification);
              await CacheHelper.removeData(key: Strings.isSubscribed);
              await CacheHelper.removeAllData();
              MagicRouter.navigateAndPopAll(const SignUpPage());
            },
            child: const CustomText(
              text: 'الدخول من جديد',
              fontSize: 15,
              textDecoration: TextDecoration.underline,
            ),
          ),
        ],
      ),
    );
  }
}
