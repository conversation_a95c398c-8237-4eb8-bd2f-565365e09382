import 'dart:developer';

import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/features/setup_account/presentation/bloc/setup_bloc.dart';
import 'package:zawaj/features/setup_account/presentation/bloc/states.dart';
import 'package:zawaj/features/setup_account/presentation/bloc/params_bloc.dart';

import '../../../../../core/constants/color_manager.dart';
import '../../../../../core/widgets/custom_text.dart';
import '../../../data/models/params_model.dart';

class MultiSelectType extends StatefulWidget {
  const MultiSelectType(
      {super.key, required this.paramsModel, required this.i, this.isUpdate, this.allowMultiSelect = false});
  final ParamsModel paramsModel;
  final int i;
  final bool? isUpdate;
  final bool allowMultiSelect;
  @override
  State<MultiSelectType> createState() => _MultiSelectTypeState();
}

class _MultiSelectTypeState extends State<MultiSelectType> {
  List<String> checkedItemValue = [];
  bool isSelectAllChecked = false;
  late final SetUpBloc setupBloc;

  @override
  void initState() {
    super.initState();
    setupBloc = SetUpBloc.get(context);
    
    // Initialize if needed
    if (setupBloc.isChecked.isEmpty || 
        setupBloc.isChecked.length <= widget.i ||
        setupBloc.isChecked[widget.i] == null) {
      if (widget.paramsModel.type == 1 && widget.paramsModel.values != null) {
        setupBloc.isChecked = List.filled(ParamsBloc.get(context).paramsList.length, null);
        setupBloc.isChecked[widget.i] = List.generate(
          widget.paramsModel.values!.length,
          (index) => Checked(
            value: false,
            index: widget.paramsModel.values![index].id
          )
        );
      }
    }
    
    // Check if all items are already selected and update isSelectAllChecked accordingly
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
      checkAndUpdateSelectAllState();
      }
    });
  }

  void checkAndUpdateSelectAllState() {
    if (!mounted) return;
    
    if (widget.allowMultiSelect && 
        setupBloc.isChecked.isNotEmpty && 
        setupBloc.isChecked[widget.i] != null) {
      
      // Check if all items are selected in the bloc
      bool allSelectedInBloc = true;
      for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
        if (setupBloc.isChecked[widget.i]![i]!.value != true) {
          allSelectedInBloc = false;
          break;
        }
      }
      
      // Check if all items are in multiSelectList
      bool allSelectedInList = true;
      for (int i = 0; i < widget.paramsModel.values!.length; i++) {
        bool exists = setupBloc.multiSelectList!.any((element) {
          return element?.paramId == widget.paramsModel.id && 
                 element?.valueId == widget.paramsModel.values![i].id;
        });
        if (!exists) {
          allSelectedInList = false;
          break;
        }
      }
      
      // If all items are selected in both places, check "Select All"
      if (allSelectedInBloc && allSelectedInList && widget.paramsModel.values!.length > 0) {
        setState(() {
          isSelectAllChecked = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isUpdate == true &&
        setupBloc.isChecked.isNotEmpty &&
        setupBloc.isChecked[widget.i] != null) {
      checkedItemValue = [];

      for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
        if (setupBloc.isChecked[widget.i]![i]!.value == true) {
          checkedItemValue.add(widget.paramsModel.values![i].value!);
        }
      }
    }
    
    return BlocBuilder<SetUpBloc, SetUpStates>(
      bloc: setupBloc,
      builder: (context, state) {
    return widget.isUpdate == true
        ? Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: ExpandablePanel(
              theme: const ExpandableThemeData(
                expandIcon: Icons.edit_outlined,
                collapseIcon: Icons.keyboard_arrow_up,
                iconColor: ColorManager.primaryColor,
              ),
              header: Padding(
                padding: const EdgeInsets.all(8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: CustomText(
                        align: TextAlign.start,
                        text: widget.allowMultiSelect && isSelectAllChecked
                            ? widget.paramsModel.values!.map((value) => value.value).join(', ')
                            : checkedItemValue
                            .toString()
                            .replaceAll('[', '')
                            .replaceAll(']', ''),
                        fontSize: 16,
                        color: ColorManager.darkGrey,
                      ),
                    )
                  ],
                ),
              ),
              expanded: BlocConsumer<SetUpBloc, SetUpStates>(
                listener: (context, state) {},
                builder: (context, state) {
                  return ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: widget.allowMultiSelect 
                        ? widget.paramsModel.values!.length + 1 
                        : widget.paramsModel.values!.length,
                    itemBuilder: (context, index) {
                      // Show "Select All" checkbox as first item when allowMultiSelect is true
                      if (widget.allowMultiSelect && index == 0) {
                        return Directionality(
                          textDirection: TextDirection.rtl,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Checkbox(
                                value: isSelectAllChecked,
                                onChanged: (value) {
                                  setState(() {
                                    isSelectAllChecked = value ?? false;
                                    
                                    if (isSelectAllChecked) {
                                      // Select all items in background (when checkbox is checked)
                                      checkedItemValue.clear();
                                      for (int i = 0; i < widget.paramsModel.values!.length; i++) {
                                        checkedItemValue.add(widget.paramsModel.values![i].value!);
                                        
                                        // Add to multiSelectList if not already exists
                                        bool exists = setupBloc.multiSelectList!.any((element) {
                                          return element?.paramId == widget.paramsModel.id && 
                                                 element?.valueId == widget.paramsModel.values![i].id;
                                        });
                                        
                                        if (!exists) {
                                          setupBloc.multiSelectList!.add(
                                            ValueBody(
                                              paramId: widget.paramsModel.id,
                                              value: widget.paramsModel.values![i].value,
                                              valueId: widget.paramsModel.values![i].id,
                                            ),
                                          );
                                        }
                                      }
                                    } else {
                                      // Deselect all items (when checkbox is unchecked)
                                      checkedItemValue.clear();
                                          setupBloc.multiSelectList!.removeWhere((element) {
                                        return element?.paramId == widget.paramsModel.id;
                                      });
                                      
                                      // Clear all isChecked values in the bloc
                                          for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                            setupBloc.isChecked[widget.i]![i]!.value = false;
                                      }
                                    }
                                  });
                                },
                              ),
                              CustomText(
                                text: "اختيار الكل",
                                fontSize: 16,
                                // fontWeight: FontWeight.bold,
                                color: ColorManager.darkGrey,
                              )
                            ],
                          ),
                        );
                      }
                      
                      // Adjust index for regular items when "Select All" is present
                      int actualIndex = widget.allowMultiSelect ? index - 1 : index;
                      
                      return Directionality(
                      textDirection: TextDirection.rtl,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Checkbox(
                              value: widget.allowMultiSelect && isSelectAllChecked 
                                  ? false 
                                  : setupBloc.isChecked[widget.i]![actualIndex]!.value ?? false,
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                    if (!widget.allowMultiSelect) {
                                      // Single selection behavior - uncheck all other checkboxes in the same group
                                          for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                        if (i != actualIndex) {
                                              setupBloc.isChecked[widget.i]![i]!.value = false;
                                        }
                                      }
                                      
                                      // Remove all previous selections for this parameter group
                                        setupBloc.multiSelectList!.removeWhere((element) {
                                        return element?.paramId == widget.paramsModel.id;
                                      });
                                      
                                      checkedItemValue.clear();
                                      checkedItemValue.add(widget.paramsModel.values![actualIndex].value!);
                                      
                                      // Add the new selection
                                          setupBloc.multiSelectList!.add(
                                        ValueBody(
                                        paramId: widget.paramsModel.id,
                                          value: widget.paramsModel.values![actualIndex].value,
                                          valueId: widget.paramsModel.values![actualIndex].id,
                                        ),
                                      );
                                    } else {
                                      // If "Select All" is active, uncheck it and select only this item
                                      if (isSelectAllChecked) {
                                        isSelectAllChecked = false;
                                        checkedItemValue.clear();
                                        setupBloc.multiSelectList!.removeWhere((element) {
                                          return element?.paramId == widget.paramsModel.id;
                                        });
                                        
                                        // Clear all isChecked values in the bloc
                                            for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                              setupBloc.isChecked[widget.i]![i]!.value = false;
                                        }
                                      }
                                      
                                      // Multi-selection behavior - check if already selected
                                      if (!checkedItemValue.contains(widget.paramsModel.values![actualIndex].value!)) {
                                        checkedItemValue.add(widget.paramsModel.values![actualIndex].value!);
                                        
                                        // Check if already exists in multiSelectList
                                        bool exists = setupBloc.multiSelectList!.any((element) {
                                          return element?.paramId == widget.paramsModel.id && 
                                                 element?.valueId == widget.paramsModel.values![actualIndex].id;
                                        });
                                        
                                        if (!exists) {
                                          setupBloc.multiSelectList!.add(
                                          ValueBody(
                                            paramId: widget.paramsModel.id,
                                              value: widget.paramsModel.values![actualIndex].value,
                                              valueId: widget.paramsModel.values![actualIndex].id,
                                            ),
                                          );
                                        }
                                      }
                                  }
                                } else {
                                    checkedItemValue.remove(widget.paramsModel.values![actualIndex].value!);
                                    setupBloc.multiSelectList!.removeWhere((element) {
                                      return element?.valueId == widget.paramsModel.values![actualIndex].id;
                                    });
                                    
                                    // If any item is unchecked, uncheck "Select All"
                                    if (widget.allowMultiSelect) {
                                      isSelectAllChecked = false;
                                    }
                                  }
                                  setupBloc.isChecked[widget.i]![actualIndex]!.value = value;
                              });
                            },
                          ),
                          CustomText(
                              text: widget.paramsModel.values![actualIndex].value ?? "",
                            fontSize: 16,
                            // fontWeight: FontWeight.bold,
                            color: ColorManager.darkGrey,
                          )
                        ],
                      ),
                      );
                    },
                  );
                },
              ),
              collapsed: const SizedBox(),
            ),
          )
        : Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: ExpandablePanel(
              theme: const ExpandableThemeData(
                iconColor: ColorManager.borderColor,
              ),
              header: Padding(
                padding: const EdgeInsets.all(8),
                child: Row(
                  children: [
                    CustomText(
                      text: widget.allowMultiSelect && isSelectAllChecked
                          ? widget.paramsModel.values!.map((value) => value.value).join(', ')
                          : checkedItemValue.toString() == '[]'
                          ? widget.paramsModel.title.toString()
                          : checkedItemValue
                              .toString()
                              .replaceAll('[', '')
                              .replaceAll(']', ''),
                      fontSize: 16,
                      color: ColorManager.darkGrey,
                    ),
                  ],
                ),
              ),
              expanded: BlocConsumer<SetUpBloc, SetUpStates>(
                listener: (context, state) {},
                builder: (context, state) {
                  return ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: widget.allowMultiSelect 
                        ? widget.paramsModel.values!.length + 1 
                        : widget.paramsModel.values!.length,
                    itemBuilder: (context, index) {
                      // Show "Select All" checkbox as first item when allowMultiSelect is true
                      if (widget.allowMultiSelect && index == 0) {
                        return Directionality(
                          textDirection: TextDirection.rtl,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Checkbox(
                                value: isSelectAllChecked,
                                onChanged: (value) {
                                  setState(() {
                                    isSelectAllChecked = value ?? false;
                                    
                                    if (isSelectAllChecked) {
                                      // Select all items in background (when checkbox is checked)
                                      checkedItemValue.clear();
                                      for (int i = 0; i < widget.paramsModel.values!.length; i++) {
                                        checkedItemValue.add(widget.paramsModel.values![i].value!);
                                        
                                        // Add to multiSelectList if not already exists
                                          bool exists = setupBloc.multiSelectList!.any((element) {
                                          return element?.paramId == widget.paramsModel.id && 
                                                 element?.valueId == widget.paramsModel.values![i].id;
                                        });
                                        
                                        if (!exists) {
                                            setupBloc.multiSelectList!.add(
                                            ValueBody(
                                              paramId: widget.paramsModel.id,
                                              value: widget.paramsModel.values![i].value,
                                              valueId: widget.paramsModel.values![i].id,
                                            ),
                                          );
                                        }
                                      }
                                    } else {
                                      // Deselect all items (when checkbox is unchecked)
                                      checkedItemValue.clear();
                                          setupBloc.multiSelectList!.removeWhere((element) {
                                        return element?.paramId == widget.paramsModel.id;
                                      });
                                      
                                      // Clear all isChecked values in the bloc
                                          for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                            setupBloc.isChecked[widget.i]![i]!.value = false;
                                      }
                                    }
                                  });
                                },
                              ),
                              CustomText(
                                text: "اختيار الكل",
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: ColorManager.darkGrey,
                              )
                            ],
                          ),
                        );
                      }
                      
                      // Adjust index for regular items when "Select All" is present
                      int actualIndex = widget.allowMultiSelect ? index - 1 : index;
                      
                      return Directionality(
                      textDirection: TextDirection.rtl,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Checkbox(
                              value: widget.allowMultiSelect && isSelectAllChecked 
                                  ? false 
                                  : setupBloc.isChecked[widget.i]![actualIndex]!.value ?? false,
                            onChanged: (value) {
                              setState(() {
                                if (value == true) {
                                    if (!widget.allowMultiSelect) {
                                      // Single selection behavior - uncheck all other checkboxes in the same group
                                          for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                        if (i != actualIndex) {
                                              setupBloc.isChecked[widget.i]![i]!.value = false;
                                        }
                                      }
                                      
                                      // Remove all previous selections for this parameter group
                                          setupBloc.multiSelectList!.removeWhere((element) {
                                        return element?.paramId == widget.paramsModel.id;
                                      });
                                      
                                      checkedItemValue.clear();
                                      checkedItemValue.add(widget.paramsModel.values![actualIndex].value!);
                                      
                                      // Add the new selection
                                          setupBloc.multiSelectList!.add(
                                        ValueBody(
                                        paramId: widget.paramsModel.id,
                                          value: widget.paramsModel.values![actualIndex].value,
                                          valueId: widget.paramsModel.values![actualIndex].id,
                                        ),
                                      );
                                    } else {
                                      // If "Select All" is active, uncheck it and select only this item
                                      if (isSelectAllChecked) {
                                        isSelectAllChecked = false;
                                        checkedItemValue.clear();
                                          setupBloc.multiSelectList!.removeWhere((element) {
                                          return element?.paramId == widget.paramsModel.id;
                                        });
                                        
                                        // Clear all isChecked values in the bloc
                                            for (int i = 0; i < setupBloc.isChecked[widget.i]!.length; i++) {
                                              setupBloc.isChecked[widget.i]![i]!.value = false;
                                        }
                                      }
                                      
                                      // Multi-selection behavior - check if already selected
                                      if (!checkedItemValue.contains(widget.paramsModel.values![actualIndex].value!)) {
                                        checkedItemValue.add(widget.paramsModel.values![actualIndex].value!);
                                        
                                        // Check if already exists in multiSelectList
                                            bool exists = setupBloc.multiSelectList!.any((element) {
                                          return element?.paramId == widget.paramsModel.id && 
                                                 element?.valueId == widget.paramsModel.values![actualIndex].id;
                                        });
                                        
                                        if (!exists) {
                                    setupBloc.multiSelectList!.add(
                                          ValueBody(
                                            paramId: widget.paramsModel.id,
                                              value: widget.paramsModel.values![actualIndex].value,
                                              valueId: widget.paramsModel.values![actualIndex].id,
                                            ),
                                          );
                                        }
                                      }
                                  }
                                } else {
                                    checkedItemValue.remove(widget.paramsModel.values![actualIndex].value!);
                                    setupBloc.multiSelectList!.removeWhere((element) {
                                      return element?.valueId == widget.paramsModel.values![actualIndex].id;
                                    });
                                    
                                    // If any item is unchecked, uncheck "Select All"
                                    if (widget.allowMultiSelect) {
                                      isSelectAllChecked = false;
                                    }
                                  }
                                  setupBloc.isChecked[widget.i]![actualIndex]!.value = value;
                              });
                            },
                          ),
                          CustomText(
                              text: widget.paramsModel.values![actualIndex].value ?? "",
                            color: Colors.black,
                          )
                        ],
                      ),
                      );
                    },
                  );
                },
              ),
              collapsed: const SizedBox(),
            ),
              );
      },
          );
  }
}
