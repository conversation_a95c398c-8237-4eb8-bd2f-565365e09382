part of 'user_by_id_bloc.dart';

abstract class UserByIdState extends Equatable {
  const UserByIdState();
  
  @override
  List<Object> get props => [];
}

class UserByIdInitial extends UserByIdState {}

class UserByIdLoading extends UserByIdState {}

class UserByIdLoaded extends UserByIdState {
  final UserByIdModel user;

  const UserByIdLoaded(this.user);

  @override
  List<Object> get props => [user];
}

class UserByIdError extends UserByIdState {
  final String message;

  const UserByIdError(this.message);

  @override
  List<Object> get props => [message];
} 