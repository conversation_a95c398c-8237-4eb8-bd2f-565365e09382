import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:zawaj/features/profile/data/models/user_by_id_model.dart';
import 'package:zawaj/features/profile/data/repository/user_by_id_repository.dart';

part 'user_by_id_event.dart';
part 'user_by_id_state.dart';

class UserByIdBloc extends Bloc<UserByIdEvent, UserByIdState> {
  final UserByIdRepository repository;

  UserByIdBloc(this.repository) : super(UserByIdInitial()) {
    on<GetUserByIdEvent>(_onGetUserById);
  }

  Future<void> _onGetUserById(
    GetUserByIdEvent event,
    Emitter<UserByIdState> emit,
  ) async {
    emit(UserByIdLoading());
    
    final result = await repository.getUserById(event.userId);
    
    result.fold(
      (failure) => emit(UserByIdError(failure)),
      (user) => emit(UserByIdLoaded(user)),
    );
  }
} 