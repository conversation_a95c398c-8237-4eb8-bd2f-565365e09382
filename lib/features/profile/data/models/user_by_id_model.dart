class UserByIdModel {
  bool? isSubscribed;
  String? planType;
  String? subscriptionId;
  String? startDate;
  String? endDate;
  bool? isBlocked;
  int? isActive;
  int? verificationId;
  String? verificationStatus;
  String? name;
  String? email;
  String? phoneNumber;
  String? about;
  String? city;
  String? area;
  String? gender;
  String? religion;
  String? maritalStatus;
  int? age;
  double? height;
  double? weight;
  int? birthYear;
  String? birthDay;
  bool? isSmoking;
  List<String>? images;
  List<ParameterUserDto>? parameters;

  UserByIdModel({
    this.isSubscribed,
    this.planType,
    this.subscriptionId,
    this.startDate,
    this.endDate,
    this.isBlocked,
    this.isActive,
    this.verificationId,
    this.verificationStatus,
    this.name,
    this.email,
    this.phoneNumber,
    this.about,
    this.city,
    this.area,
    this.gender,
    this.religion,
    this.maritalStatus,
    this.age,
    this.height,
    this.weight,
    this.birthYear,
    this.birthDay,
    this.isSmoking,
    this.images,
    this.parameters,
  });

  factory UserByIdModel.fromJson(Map<String, dynamic> json) => UserByIdModel(
        isSubscribed: json["isSubscribed"],
        planType: json["planType"],
        subscriptionId: json["subscriptionId"],
        startDate: json["startDate"],
        endDate: json["endDate"],
        isBlocked: json["isBlocked"],
        isActive: json["isActive"],
        verificationId: json["verificationId"],
        verificationStatus: json["verificationStatus"],
        name: json["name"],
        email: json["email"],
        phoneNumber: json["phoneNumber"],
        about: json["about"],
        city: json["city"],
        area: json["area"],
        gender: json["gender"],
        religion: json["religion"],
        maritalStatus: json["maritalStatus"],
        age: json["age"],
        height: json["height"]?.toDouble(),
        weight: json["weight"]?.toDouble(),
        birthYear: json["birthYear"],
        birthDay: json["birthDay"],
        isSmoking: json["isSmoking"],
        images: json["images"] != null ? List<String>.from(json["images"]) : null,
        parameters: json["parameters"] != null
            ? List<ParameterUserDto>.from(
                json["parameters"].map((x) => ParameterUserDto.fromJson(x)))
            : null,
      );

  static UserByIdModel empty() {
    return UserByIdModel(
      isSubscribed: false,
      planType: null,
      subscriptionId: null,
      startDate: null,
      endDate: null,
      isBlocked: false,
      isActive: 0,
      verificationId: null,
      verificationStatus: null,
      name: '',
      email: '',
      phoneNumber: '',
      about: '',
      city: '',
      area: '',
      gender: '',
      religion: '',
      maritalStatus: '',
      age: null,
      height: null,
      weight: null,
      birthYear: null,
      birthDay: null,
      isSmoking: null,
      images: [],
      parameters: [],
    );
  }
}

class ParameterUserDto {
  int? parameterId;
  String? parameterName;
  int? parameterType;
  int? valueId;
  String? valueName;

  ParameterUserDto({
    this.parameterId,
    this.parameterName,
    this.parameterType,
    this.valueId,
    this.valueName,
  });

  factory ParameterUserDto.fromJson(Map<String, dynamic> json) => ParameterUserDto(
        parameterId: json["parameterId"],
        parameterName: json["parameterName"],
        parameterType: json["parameterType"],
        valueId: json["valueId"],
        valueName: json["valueName"],
      );

  Map<String, dynamic> toJson() => {
        "parameterId": parameterId,
        "parameterName": parameterName,
        "parameterType": parameterType,
        "valueId": valueId,
        "valueName": valueName,
      };
} 