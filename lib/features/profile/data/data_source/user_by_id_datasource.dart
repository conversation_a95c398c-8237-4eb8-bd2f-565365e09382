import 'package:dio/dio.dart';
import 'package:zawaj/core/constants/end_points.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/helper/cache_helper.dart';

class UserByIdDataSource {
  final Dio dio = Dio();

  Future<Response> getUserById(String userId) async {
    String? token = await CacheHelper.getData(key: Strings.token);
    
    return await dio.get(
      '${EndPoints.BASE_URL}HomeUser/GetProfileById',
      queryParameters: {
        'UserId': userId,
        'lang': 'ar',
      },
      options: Options(headers: {
        'Authorization': 'Bearer $token',
      }),
    );
  }
} 