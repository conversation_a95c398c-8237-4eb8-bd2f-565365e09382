import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:zawaj/core/helper/api_error_handler.dart';
import 'package:zawaj/core/network/network_info.dart';
import 'package:zawaj/features/profile/data/data_source/user_by_id_datasource.dart';
import 'package:zawaj/features/profile/data/models/user_by_id_model.dart';

class UserByIdRepository {
  final UserByIdDataSource dataSource;
  final NetworkInfo networkInfo;

  UserByIdRepository({
    required this.dataSource,
    required this.networkInfo,
  });

  Future<Either<String, UserByIdModel>> getUserById(String userId) async {
    if (await networkInfo.isConnected) {
      try {
        Response response = await dataSource.getUserById(userId);
        if (response.statusCode == 200) {
          UserByIdModel user = UserByIdModel.fromJson(response.data);
          return Right(user);
        } else {
          return Left(ApiExceptionHandler.getMessage(response));
        }
      } on DioException catch (error) {
        return Left(ApiExceptionHandler.getMessage(error));
      }
    } else {
      return const Left(Strings.nointernet);
    }
  }
} 