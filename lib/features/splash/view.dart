import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/extensions/colored_print.dart';
import 'package:zawaj/core/extensions/sizes.dart';
import 'package:zawaj/core/widgets/custom_scaffold.dart';
import 'package:zawaj/core/widgets/logo_image.dart';
import 'package:zawaj/features/authentication/presentation/pages/login_signup/login_page.dart';
import 'package:zawaj/features/authentication/presentation/pages/login_signup/signup_page.dart';
import 'package:zawaj/features/authentication/presentation/pages/login_signup/signup_success_screen.dart';
import 'package:zawaj/features/authentication/presentation/pages/otp/enter_name_phone_Screen.dart';
import 'package:zawaj/features/dashboard/view.dart';
import 'package:zawaj/features/payment/presentation/pages/payment_possibilities.dart';
import 'package:zawaj/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:zawaj/features/profile/presentation/pages/verification/presentation/verifing_request_sent_screen.dart';
import 'package:zawaj/features/setup_account/presentation/pages/gender_screen.dart';
import 'package:zawaj/features/setup_account/presentation/pages/rejectionPage.dart';
import 'package:zawaj/features/setup_account/presentation/pages/your_profile_is_complete.dart';
import '../../core/constants/strings.dart';
import '../../core/helper/cache_helper.dart';
import '../../core/router/routes.dart';
import '../onBoarding/view.dart';
import 'package:flutter/services.dart';
import 'dart:developer';

class SplashView extends StatefulWidget {
  const SplashView({super.key});

  @override
  State<SplashView> createState() => _SplashViewState();
}

class _SplashViewState extends State<SplashView> {
  @override
  void initState() {
    super.initState();
    ProfileBloc.get(context).getMyProfile();

    _navigateToNextScreen();
  }

  void _navigateToNextScreen() {
    Future.delayed(const Duration(seconds: 3), () async {
      bool? isFirst = await CacheHelper.getData(key: Strings.isFirst);
      String? token = await CacheHelper.getData(key: Strings.token);
      bool hasSetup = await CacheHelper.getData(key: Strings.hasSetup) ?? false;
      bool hasRequired = await CacheHelper.getData(key: Strings.hasRequired) ?? false;
      String? verificationState = CacheHelper.getData(key: Strings.verificationState);
      bool emailConfirmed = CacheHelper.getData(key: Strings.emailConfirmed) ?? false;
      bool phoneConfirmed = CacheHelper.getData(key: Strings.phoneConfirmed) ?? false;
      bool isSubscribed = CacheHelper.getData(key: Strings.isSubscribed) ?? false;
      
      // Check if user is in re-verification process
      bool isInReVerification = CacheHelper.getData(key: Strings.isInReVerification) ?? false;

      log('isFirst ============> $isFirst');
      log('token ============> $token');
      log('hasSetup ============> $hasSetup');
      log('hasRequired ============> $hasRequired');
      log('verificationState ============> $verificationState');
      log('emailConfirmed ============> $emailConfirmed');
      log('phoneConfirmed ============> $phoneConfirmed');
      log('isSubscribed ============> $isSubscribed');
      log('isInReVerification ============> $isInReVerification');

      isFirst == true && hasSetup == false
          ? MagicRouter.navigateAndReplacement(const OnBoarding())
          : token == null
              ? MagicRouter.navigateAndReplacement(const LoginPage())
              : emailConfirmed == false
                  ? MagicRouter.navigateAndReplacement(const SignUpPage())
                  : phoneConfirmed == false &&
                          isSubscribed == false &&
                          hasSetup == false &&
                          verificationState == 'Not Verified'
                      ? MagicRouter.navigateAndReplacement(
                          const EnterPhoneAndName())
                      : hasRequired && isSubscribed == true
                          ? MagicRouter.navigateAndReplacement(
                              const DashBoardScreen(
                              initialIndex: 2,
                            ))
                          : phoneConfirmed == true &&
                                      verificationState == 'Not Verified' ||
                                  phoneConfirmed == true &&
                                      verificationState == null
                              ? MagicRouter.navigateAndReplacement(
                                  const SignupSuccess())
                              : isInReVerification == true && verificationState == 'Pending'
                                  ? MagicRouter.navigateAndReplacement(
                                      const VerificationRequestSent())
                              : isInReVerification == true && verificationState == 'Rejected'
                                  ? MagicRouter.navigateAndReplacement(
                                      const VerificationRequestSent())
                                  : verificationState == 'Rejected'
                                      ? MagicRouter.navigateAndReplacement(
                                          const YourProfileIsRejected())
                                      : hasSetup == false &&
                                              verificationState == 'Pending' &&
                                              phoneConfirmed == true
                                          ? MagicRouter.navigateAndReplacement(
                                              const GenderScreen())
                                          : hasSetup == true &&
                                                  verificationState == 'Pending' &&
                                                  isSubscribed == true
                                              ? MagicRouter.navigateAndReplacement(
                                                  const VerificationRequestSent())
                                              : hasSetup == true &&
                                                      verificationState ==
                                                          'Accepted' &&
                                                      isSubscribed == true
                                                  ? MagicRouter.navigateAndReplacement(
                                                      const YourProfileIsComplete())
                                                  : hasSetup == true &&
                                                          verificationState ==
                                                              'Cancelled'
                                                      ? MagicRouter
                                                          .navigateAndReplacement(
                                                              const SignUpPage())
                                                      : isSubscribed == false &&
                                                              hasSetup == true &&
                                                              (verificationState ==
                                                                      'Pending' ||
                                                                  verificationState ==
                                                                      'Accepted')
                                                          ? MagicRouter
                                                              .navigateAndReplacement(
                                                                  const PayementPossibility(
                                                              isFromProfileScreen:
                                                                  false,
                                                            ))
                                                          : MagicRouter
                                                              .navigateAndReplacement(
                                                                  const GenderScreen());

      //   MagicRouter.navigateAndReplacement(const DashBoardScreen());
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: const SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
      ),
      child: CustomScaffold(
          child: Blur(
        blur: 20,
        blurColor: Colors.transparent,
        colorOpacity: 0,
        overlay: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          // crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: EdgeInsets.all(50),
              child: LogoImage(),
            )
          ],
        ),
        child: Container(
          height: context.height,
          width: context.width,
          decoration: BoxDecoration(
              gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            // stops: [0.1, 0.2, 0.3, 0.4],
            colors: [
              ColorManager.splashTopColor.withOpacity(0.7),
              ColorManager.fadePinkColor.withOpacity(0.5),
              ColorManager.fadePinkColor,
              ColorManager.splashBottomColor.withOpacity(0.02),
            ],
          )),
          //  child:
        ),
      )),
    );
  }
}
