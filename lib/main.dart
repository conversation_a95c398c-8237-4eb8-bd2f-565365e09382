import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:zawaj/app.dart';
import 'package:zawaj/core/constants/strings.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'core/helper/bloc_observer.dart';
import 'core/helper/cache_helper.dart';
import 'core/helper/dio_helper.dart';
import 'injection_controller.dart' as di;

final canShowWidgets = DateTime.now().isAfter(DateTime.parse('2024-12-05'));

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  await EasyLocalization.ensureInitialized();
  await CacheHelper.init();
  await di.init();
  await DioHelper.init();
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  try {
    OneSignal.initialize("************************************");
    await OneSignal.Notifications.requestPermission(true);

    String? id = OneSignal.User.pushSubscription.id;

    CacheHelper.setData(key: Strings.DEVICEID, value: id ?? 'fake');
  } catch (e) {
    log('kkk$e');
  }

  OneSignal.User.pushSubscription.addObserver((state) {
    CacheHelper.setData(
        key: Strings.DEVICEID,
        value: OneSignal.User.pushSubscription.id ?? 'fake');
  });

  String? deviceId = await CacheHelper.getData(
    key: Strings.DEVICEID,
  );
  if (deviceId == null || deviceId == '' || deviceId == 'fake') {
    await CacheHelper.setData(
        key: Strings.DEVICEID,
        value: OneSignal.User.pushSubscription.id ?? 'fake');
  }

  // Add this line to lock the orientation to portrait mode only
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  Bloc.observer = SimpleBlocObserver();

  EasyLocalization.logger.enableBuildModes = [];

  runApp(Phoenix(
    child: EasyLocalization(
      supportedLocales: const [Locale('ar'), Locale('en')],
      path: 'assets/languages',
      fallbackLocale: const Locale('ar', ''),
      startLocale: const Locale('ar', ''),
      child: const MyApp()
    )
  ));
}
