import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:signalr_core/signalr_core.dart';
import 'package:zawaj/core/constants/end_points.dart';

class SignalRService {
  static HubConnection? connection;
  static Completer<void>? connectionCompleter;
  
  // Online users management
  static List<String> onlineUserIds = [];
  static Function(List<String>)? onOnlineUsersChanged;
  static Function(String)? onUserOnline;
  static Function(String)? onUserOffline;

  // App lifecycle management
  static bool _isAppActive = true;
  static String? _currentUserId;

  static Future<void> connectAndStart({required String userId}) async {
    try {
      _currentUserId = userId;
      
      // Construct the URL with userId as query parameter
      final url = '${EndPoints.hubURL}$userId';
      // log('Connecting to SignalR: $url');
      
      connection = HubConnectionBuilder().withUrl(url,
          HttpConnectionOptions(
        logging: (level, message) {
          // log('SignalR Logging: $message');
        },
      )).build();
      
      // log('SignalR State: ${connection!.state}');

      // Set up event handlers for online/offline detection
      _setupOnlineOfflineHandlers();

      await connection?.start();
      // log('SignalR connection established');

      // Note: Online users list is automatically sent by the server via 'OnlineUsers' event
      // No need to request it manually

    } catch (e) {
      log('Error establishing SignalR connection: $e');
    }
  }

  static void _setupOnlineOfflineHandlers() {
    // Handle when a user comes online
    connection?.on('UserIsOnline', (List<dynamic>? args) {
      if (args != null && args.isNotEmpty) {
        final userId = args.first.toString();
        // log('User came online: $userId');
        // log('Current online users before: $onlineUserIds');
        
        if (!onlineUserIds.contains(userId)) {
          onlineUserIds.add(userId);
          // log('Current online users after: $onlineUserIds');
          onUserOnline?.call(userId);
          onOnlineUsersChanged?.call(onlineUserIds);
        }
      }
    });

    // Handle when a user goes offline
    connection?.on('UserIsOffline', (List<dynamic>? args) {
      if (args != null && args.isNotEmpty) {
        final userId = args.first.toString();
        // log('User went offline: $userId');
        
        if (onlineUserIds.contains(userId)) {
          onlineUserIds.remove(userId);
          onUserOffline?.call(userId);
          onOnlineUsersChanged?.call(onlineUserIds);
        }
      }
    });

    // Handle initial online users list
    connection?.on('OnlineUsers', (List<dynamic>? args) {
      if (args != null && args.isNotEmpty) {
        // log('OnlineUsers raw args: $args');
        
        // Handle nested array format from backend
        List<String> initialOnlineUsers = [];
        if (args.first is List) {
          // If the first element is a list, flatten it
          initialOnlineUsers = (args.first as List)
              .map((user) => user.toString())
              .toList();
        } else {
          // Direct list format
          initialOnlineUsers = args
              .map((user) => user.toString())
              .toList();
        }
        
        // log('Initial online users processed: $initialOnlineUsers');
        onlineUserIds = initialOnlineUsers;
        // log('Online users list updated: $onlineUserIds');
        onOnlineUsersChanged?.call(onlineUserIds);
      } else {
        log('OnlineUsers event received but args is empty or null');
      }
    });

    // Keep existing message handler
    connection?.on('ReceiveMessage', (List<dynamic>? args) {
      log('Received message: ${args.toString()}');
    });
  }

  // App lifecycle management methods
  static void handleAppLifecycleState(AppLifecycleState state) {
    log('=== APP LIFECYCLE STATE CHANGED: $state ===');
    
    switch (state) {
      case AppLifecycleState.resumed:
        // log('App resumed - marking user as online');
        _isAppActive = true;
        _reconnectIfNeeded();
        _notifyUserOnline();
        break;
      case AppLifecycleState.inactive:
        // log('App inactive - no action needed');
        // App is transitioning (e.g., receiving a phone call)
        break;
      case AppLifecycleState.paused:
        // log('App paused - marking user as offline');
        _isAppActive = false;
        _notifyUserOffline();
        break;
      case AppLifecycleState.detached:
        // log('App detached - marking user as offline');
        _isAppActive = false;
        _notifyUserOffline();
        break;
      case AppLifecycleState.hidden:
        // log('App hidden - marking user as offline');
        _isAppActive = false;
        _notifyUserOffline();
        break;
    }
  }

  static void _notifyUserOffline() {
    log('=== NOTIFYING USER OFFLINE ===');
    log('Current user ID: $_currentUserId');
    log('Connection state: ${connection?.state}');
    
    if (_currentUserId != null && connection?.state == HubConnectionState.connected) {
      log('Notifying server that user is going offline: $_currentUserId');
      try {
        // Send a custom event to notify the server that user is going offline
        // The backend should handle this to mark the user as offline
        connection?.invoke('UserOffline', args: [_currentUserId]);
        // log('UserOffline event sent successfully to server');
      } catch (e) {
        log('Error notifying server about user going offline: $e');
      }
    } else {
      // log('Cannot notify server - user ID is null or connection is not established');
      // log('User ID: $_currentUserId');
      // log('Connection state: ${connection?.state}');
    }
  }

  static void _reconnectIfNeeded() {
    if (_currentUserId != null && connection?.state != HubConnectionState.connected) {
      // log('Reconnecting SignalR after app resume');
      connectAndStart(userId: _currentUserId!);
    }
  }

  static void _notifyUserOnline() {
    if (_currentUserId != null && connection?.state == HubConnectionState.connected) {
      // log('Notifying server that user is back online: $_currentUserId');
      try {
        // Send a custom event to notify the server that user is back online
        connection?.invoke('UserOnline', args: [_currentUserId]);
      } catch (e) {
        log('Error notifying server about user coming back online: $e');
      }
    }
  }

  // Removed _requestOnlineUsers method since the server automatically sends OnlineUsers event

  // Check if a specific user is online
  static bool isUserOnline(String userId) {
    return onlineUserIds.contains(userId);
  }

  // Get all online users
  static List<String> getOnlineUsers() {
    return List.from(onlineUserIds);
  }

  // Set up callbacks for UI updates
  static void setOnlineUsersChangedCallback(Function(List<String>) callback) {
    onOnlineUsersChanged = callback;
  }

  static void setUserOnlineCallback(Function(String) callback) {
    onUserOnline = callback;
  }

  static void setUserOfflineCallback(Function(String) callback) {
    onUserOffline = callback;
  }

  // Send message (existing functionality)
  static Future<void> sendMessage(String senderId, String receiverId, String message) async {
    if (connection?.state == HubConnectionState.connected) {
      try {
        await connection?.invoke('SendMessage', args: [senderId, receiverId, message]);
        // log('Message sent successfully');
      } catch (e) {
        log('Error sending message: $e');
        rethrow;
      }
    } else {
      log('SignalR connection is not established');
      throw Exception('SignalR connection is not established');
    }
  }

  // Disconnect
  static Future<void> disconnect() async {
    log('=== SIGNALR DISCONNECT CALLED ===');
    log('Current user ID: $_currentUserId');
    log('Connection state: ${connection?.state}');
    
    try {
      // Notify server before disconnecting
      _notifyUserOffline();
      
      await connection?.stop();
      // log('SignalR connection stopped successfully');
      // log('User should now appear offline to other users');
    } catch (e) {
      log('Error stopping SignalR connection: $e');
    }
  }

  // Handle app termination (called when app is about to be killed)
  static void handleAppTermination() {
    // log('App is being terminated, notifying server');
    _notifyUserOffline();
  }

  // Check connection status
  static bool get isConnected => connection?.state == HubConnectionState.connected;
  
  // Check if app is active
  static bool get isAppActive => _isAppActive;
}
