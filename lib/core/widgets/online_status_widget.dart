import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:zawaj/core/constants/color_manager.dart';
import 'package:zawaj/core/helper/signalr.dart';

class OnlineStatusWidget extends StatefulWidget {
  final String userId;
  final double dotSize;
  final bool showLabel;
  final String? labelText;

  const OnlineStatusWidget({
    Key? key,
    required this.userId,
    this.dotSize = 8.0,
    this.showLabel = false,
    this.labelText,
  }) : super(key: key);

  @override
  State<OnlineStatusWidget> createState() => _OnlineStatusWidgetState();
}

class _OnlineStatusWidgetState extends State<OnlineStatusWidget> {
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    _updateOnlineStatus();
    _setupSignalRCallbacks();
  }

  void _setupSignalRCallbacks() {
    SignalRService.setUserOnlineCallback((userId) {
      log('OnlineStatusWidget: User online callback received for: $userId, widget userId: ${widget.userId}');
      if (userId == widget.userId) {
        log('OnlineStatusWidget: Setting user online for: $userId');
        setState(() {
          _isOnline = true;
        });
      }
    });

    SignalRService.setUserOfflineCallback((userId) {
      if (userId == widget.userId) {
        setState(() {
          _isOnline = false;
        });
      }
    });

    SignalRService.setOnlineUsersChangedCallback((onlineUsers) {
      log('OnlineStatusWidget: Online users changed callback received: $onlineUsers, widget userId: ${widget.userId}');
      setState(() {
        _isOnline = onlineUsers.contains(widget.userId);
      });
      log('OnlineStatusWidget: Updated online status for ${widget.userId}: $_isOnline');
    });
  }

  void _updateOnlineStatus() {
    setState(() {
      _isOnline = SignalRService.isUserOnline(widget.userId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (_isOnline) ...[
          Container(
            width: widget.dotSize,
            height: widget.dotSize,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.green,
            ),
          ),
        ],
        if (widget.showLabel) ...[
          if (_isOnline) const SizedBox(width: 2),
          Flexible(
            child: Text(
              widget.labelText ?? (_isOnline ? 'Online' : ''),
              style: TextStyle(
                fontSize: 10,
                color: _isOnline ? Colors.green : Colors.grey,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ],
    );
  }
}

// A simpler version that just shows the dot
class OnlineDotWidget extends StatefulWidget {
  final String userId;
  final double size;

  const OnlineDotWidget({
    Key? key,
    required this.userId,
    this.size = 8.0,
  }) : super(key: key);

  @override
  State<OnlineDotWidget> createState() => _OnlineDotWidgetState();
}

class _OnlineDotWidgetState extends State<OnlineDotWidget> {
  bool _isOnline = false;

  @override
  void initState() {
    super.initState();
    _updateOnlineStatus();
    _setupSignalRCallbacks();
  }

  void _setupSignalRCallbacks() {
    SignalRService.setUserOnlineCallback((userId) {
      if (userId == widget.userId) {
        setState(() {
          _isOnline = true;
        });
      }
    });

    SignalRService.setUserOfflineCallback((userId) {
      if (userId == widget.userId) {
        setState(() {
          _isOnline = false;
        });
      }
    });

    SignalRService.setOnlineUsersChangedCallback((onlineUsers) {
      setState(() {
        _isOnline = onlineUsers.contains(widget.userId);
      });
    });
  }

  void _updateOnlineStatus() {
    setState(() {
      _isOnline = SignalRService.isUserOnline(widget.userId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return _isOnline 
      ? Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.green,
          ),
        )
      : const SizedBox.shrink();
  }
} 