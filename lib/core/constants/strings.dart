class Strings {
  static const String Data = "الاسم و العمر";
  static const String LOGIN_NOW = "سجّل للدّخول الآن!";
  static const String register = "  انشاء حساب";
  static const String about_me = "عن نفسي";
  static const String accept_terms_conditions = "أوافق على الشروط والأحكام";
  static const String add_payment_method = "للمتابعة أضف طريقة الدفع";
  static const String add_photos = "أضف صورك";
  static const String update_photos = "تحديث صورك";
  static const String birthyear = "تاريخ الميلاد ";
  static const String choose_birthyear = "اختر تاريخ ميلادك ";
  static const String birthyear2 = "سنه الميلاد";
  static const String theHeight = "الطول";
  static const String by_phone = "عن طريق الجوال";
  static const String cancelpills = "  إلغاء الفواتير المتكررة في أي وقت";
  static const String caracter_password = "يجب أن تحتوي كلمة المرور على 8 أحرف";

  static const String my_photos = "صوري";
  static const String continu = "متابعة";

  static const String my_specifications = 'مواصفاتي';
  static const String where_are_you_from = 'من اين انت ؟';
  static const String birth_date = 'تاريخ الولادة';
  static const String change_email = "تغيير البريد الإلكتروني";
  static const String check_account = "تحقق من حسابك";
  static const String check_email = "للمتابعة، قم بالتحقق من بريدك الإلكتروني";
  static const String check_your_email =
      "للمتابعة, يرجى التحقق من بريدك الالكتروني وفتح الرابط الذي تم ارساله اليك";
  static const String commited =
      "الرجاء تأكيد التزامك بالبحث الجاد عن شريك الحياة من خلال الموافقة على الشروط التالية:";
  static const String registar_deatlis = ' سجل التفاصيل الاتية:';
  static const String choose_bundle = " اختر الرزمة المناسبة لك";
  static const String complete = "اكمل";
  static const String nameIsTriple = "الاسم ثلاثي";
  static const String complete_setup =
      "ملفك الشخصي قد اكتمل بنجاح، ويمكنك البدء في البحث عن شريك/ ة حياتك الآن.";
  static const String safteyInsurance = "ضمان الجدية والأمان";
  static const String confirm = "تأكيد";
  static const String contactwithadmin = " افحص مره اخرى ";

  static const String saveChnages = 'حفظ التغييرات';
  static const String my_name_in_arabic = 'اسمي بالعربية';
  static const String add_new_photos = 'أضف صور جديدة';
  static const String who_are_you = "من أكون ؟";
  static const String congrats = "تهانينا!";
  static const String lastStep = "الخطوة الأخيرة وانتهينا";

  static const String continue_setup =
      "من اجل زيادة احتمالات عثورك على الشريك/ة المناسب/ة ، عليك تسجيل التفاصيل الآتية:";

  static const String complete_specifications = 'المواصفات كاملة';
  static const String increase_your_chance =
      'لزيادة فرصة عثورك على الشريك المناسب ننصحك بإكمال تسجيل مواصفاتك';
  static const String continue_with_phone = "المتابعة باستخدام رقم الهاتف.";
  static const String create_account = " قم بإنشاء حساب مجاني.";
  static const String delete_account = "حذف الحساب";
  static const String desc = "وصف";
  static const String listIsEmpty = "القائمة فارغة";
  static const String divorcedWithChildren = "منفصل مع اولاد";
  static const String divorcedWithNoChildren = "منفصل بدون اولاد";
  static const String dont_receive_code = "لم تتلق أي رمز؟ إعادة مرة أخرى";
  static const String email = "البريد الإلكتروني";
  static const String email_address = "عنوان البريد الإلكتروني";
  static const String email_sent = "تم إرسال البريد الإلكتروني.";
  static const String empty_notif = "حتى الآن لم يعجبك أحد. كن أول من يعجبه";
  static const String enter_email_send_link =
      "ادخل بريدك الإلكتروني وسنرسل اليك رابطًا لإعادة تعيين كلمة المرور.";

  static const String enter_otp = " أدخل كود التحقق ";
  static const String enter_otp_from_email =
      "ادخل رمز التحقق الذي تم ارساله الى بريدك الالكتروني";

  static const String imageAndName = 'الاسم والصور';
  static const String enter_phone = "أدخل رقم هاتفك";
  static const String exit = "الخروج";
  static const String facebook_sign_in = "المتابعة باستخدام فيسبوك";
  static const String finished = "منتهي";
  static const String agreeTerms =
      "ألتزم بشروط الاستخدام وسياسة التطبيق الموجودة في الموقع لقراءة شروط الاستخدام الكاملة يرجى";

  static const String forget_password = "نسيت كلمة المرور؟";
  static const String free = "مجاني";
  static const String free_bundle_instructions =
      '• خدمة البحث في قاعدة البيانات عن مواصفات الشريك الذي يناسبك.\n \n'
      '• خدمة الحصول على اقتراحات بما يتناسب مع طلبك.\n \n'
      '• خدمة المراسلة عبر الدردشة الكتابية (تشات) لمدة غير محدودة.\n \n'
      '• خدمة مشاهدة فيديو تعريفي عن كل مشترك عبر التشات بشكل مباشر.\n \n'
      '• خدمة معرفة هوية الأشخاص الذين أعجبوا بالملف الشخصي.';
  static const String enterPayCard = "  أدخل رقم بطاقة ائتمان ";
  static const String enterValidPayCard = "   أدخل رقم بطاقة ائتمان صالح    ";
  static const String payementInstructions =
      "  موافقتك على الحصول على وصول فوري إلى اشتراكك، وأنه إذا لم تلغي قبل انتهاء الاشتراك في 26 مارس 2023، سيتم خصم رسوم الاشتراك تلقائيًا شهريًا حتى تقوم بالإلغاء. لن تكون مؤهلاً لاسترداد أموال عند الإلغاء.";

  static const String get_best = "إحصل على الأفضل";
  static const String real_photo_description =
      'بامكانكم رفع صورة مظلّلَة او غير حقيقية للملف الشخصي ولكن ننصحكم بأن الصورة الحقيقية تعزز الثقة بين المستخدمين وتجعل ملفك الشخصي أكثر جاذبية.';
  static const String the_importance_of_the_image = "أهمية الصورة";
  static const String golden_bundle = "الرزمة الذهبية ";
  static const String golden_bundle_button = "  ابتداء من 400 شيقل";
  static const String google_sign_in = "المتابعة باستخدام جوجل";
  static const String apple_sign_in = "المتابعة باستخدام ابل";
  static const String gopaymentbutton = "انقر هنا لتنتقل الى الرزمة الذهبية";

  static const String have_account = "هل لديك حساب؟";
  static const String have_no_account = "ليس لديك حساب؟";
  static const String heDivorced = "منفصل";
  static const String divorced = "منفصل/منفصلة";
  static const String widowerWithChildern = "ارملة مع اولاد";
  static const String hello = "مرحبا";
  static const String id = "الرقم التعريفي:";
  static const String image = "صورة";
  static const String impression = "انطباعك";
  static const String must_subscribe =
      "يجب عليك الاشتراك من اجل مشاهدة هذا الحساب";

  // static const String increase_watchers =
  //     "الحقيقة المثبتة تؤكد أن نشر صورك الشخصية الحقيقية   تزيد من عدد المشاهدين لملفك الشخصي، وتجعله اكثر جاذبية.";
  static const String increase_watchers =
      "بامكانكم رفع صورة مظلّلَة او غير حقيقية للملف الشخصي ولكن ننصحكم بأن الصورة الحقيقية تعزز الثقة بين المستخدمين وتجعل ملفك الشخصي أكثر جاذبية..";
  static const String swear =
      "أُقسِمُ باللهِ العظيمِ أنني أنْوِي الزّواجَ الحلال بصدقٍ وإخلاصٍ وأمانة، وأُشهِدُ اللهَ العظيمَ أنّني سأتصرّفُ بمُنتهى الجِدِّيّة وسأحترمُ التّعارفَ المتبادَلَ بيني وبينَ الآخرينَ مِنَ المُــنتَسِبين والمــُنتَسِبات، ولَنْ أقومَ بأيِّ إخلالٍ بنظامِ التّطبيقِ أوِ الموقعِ أوْ بالأخلاقِ العامّة والآداب الأصيلة والأعراف المجتمعيّة.  ";
  static const String termSwear =
      "المستخدم مسؤول عن المحتوى والمعلومات (بما في ذلك الملفّ الشّخصيّ والصّور) الّتي ينشرها أو يرسلها عبر خدمات موقع وتطبيق زواج 48، ولا يحقّ له التّشهير أو الإساءة للأعضاء الآخرين في الموقع والتطبيق، كما لا يحقّ له استخدام ألفاظ أو صور مسيئة سواء كانت جنسيّة أو مهينة أو فيها شتم للأعضاء الآخرين، وفيما لو تمّ ارتكاب هذه المخالفات فإنّ إدارة موقع وتطبيق زواج 48 لها الحقّ في أن تقوم بتجميد العضويّة أو إلغائها ومقاضاة المخالفين حسب القانون.";
  static const String clickHere = "انقر هنا";

  static const String takePhoto =
      "قم بتصوير صورتين:\n \n  ١. صورة سيلفي واضحة للوجه\n ٢. صورة بطاقة الهوية مع الحالة الشخصية";

  static const String isDialogShown = 'isDialogShown';
  static const String isFirst = 'isFirst';

  ///------------------- text cached -----------------------------

  static const String language = 'lang';
  static const String hasSetup = 'hasSetup';
  static const String userId = 'userId';
  static const String hasRequired = 'hasRequired';
  static const String lastChat = "  19 يناير 2023 الساعة 4:14 مساءً";
  static const String likes = "الإعجابات";
  static const String login = "تسجيل الدخول";
  static const String emailConfirmed = 'emailConfirmed';
  static const String phoneConfirmed = 'phoneConfirmed';

  ///------------------- text messages -----------------------------
  static const String login_success = "تم الدخول بنجاح ";

  static const String login_to_reach_account =
      "سجل الدخول ليمكنك الوصول الى حسابك";

  ///------------------- text translated -----------------------------

  static const String onboarding = 'سبيلك الى الزواج الامن يبدأ من هنا';

  static const String or = "او";
  static const String other = "اخرى";
  static const String others = "آخر";
  static const String package = "الرزمة";
  static const String partner = "   المواصفات المطلوبة للشريك";
  static const String password = "كلمة المرور";
  static const String password_again = "أدخل كلمة المرور مرة أخرى";
  static const String payment = "الدفع";
  static const String person_name = "الإسم الشخصي";
  static const String phone_no = "رقم الهاتف";
  static const String plz_check_your_mail =
      "من فضلك، قم بفحص بريدك الإلكتروني وانقر على الرابط المقدم للتحقق من عنوانك.";

  static const String ranged_age = "الفئة العمرية";
  static const String age = "عمر";

  static const String re_enter_pass = "إعادة تعيين كلمة المرور";
  static const String regigestersuccessfully = "تم انشاء الحساب بنجاح";
  static const String registerSuccess = "تم تسجيلك بنجاح   ";
  static const String requestSent = "لقد تم ارسال طلبك بنجاح     ";
  static const String reviewRequst =
      "  سيتم مراجعة الطلب خلال 48 ساعة و إعلامك بالنتيجة عبر الإشعارات   ";
  static const String rejectionreason =
      "  في حال لم يصلك الرد تواصل مع الدعم لمعرفة المشكلة أو أسباب الرفض   ";
  static const String registerextraSteps =
      "لضمان الجدية والأمان في البحث عن شريك حياتك المناسب, نطلب منك القيام بخطوتين إضافيتين.     ";
  static const String report = "الإبلاغ عن مشكلة";
  static const String report_user = "التقرير عن المستخدم";
  static const String resend_email = "إعادة إرسال البريد الإلكتروني للتحقق.";
  static const String review = "تقييم للخدمة";
  static const String save = "حفظ";
  static const String search_for = "أبحث عن";
  static const String select_partner = " حدد مواصفات شريكك .";
  static const String send = "إرسال";
  static const String send_impression = "اترك رأيك أو رغبتك";
  static const String send_link = "إرسال الرابط";
  static const String setting = "إعدادات";
  static const String sheDivorced = "منفصلة";
  static const String single = "أعزب/ عزباء";
  static const String enterMaritalStatus = "أدخل الحالة الأجتماعية";
  static const String religion = "  اختر الديانة";
  static const String skip = "تجاوز";
  static const String some_information_about = "بعض المعلومات عنك";
  static const String startChat = "لقد تطابقت مع المستخدم يوم الاثنين 12";
  static const String the_login = "التسجيل";
  static const String token = 'token';
  static const String isSubscribed = 'isSubscribed';
  static const String verificationState = 'verificationState';
  static const String otpToken = 'otpToken';
  static const String receiverUserIdChat = 'receiverUserIdChat';
  static const String undersrtand = "فهمت";
  static const String user_name = "اسم المستخدم";
  static const String validation_password =
      "يجب ان تحتوي كلمة المرور على تفاصيل على حرف كبير,  رقم  رمز واحد على الأقل.";

  static const String we_send_msg_to_you =
      "تمّ إرسال رسالة الى بريدك الالكتروني الآتي:";
  static const String send_msg_to_you =
      "   تمّ إرسال رسالة برقم التحقق الى رقم هاتف   ";

  static const String welcome = "مرحبا بك";
  static const String welcome_back = "مرحبًا بعودتك";
  static const String who_i =
      "من أنا؟ ( عرف عن نفسك بكلمات قليلة، مثل: مهنتك، ثقافتك،هواياتك، طموحاتك ومعلومات اخرى تراها مهمة للتعارف).";

  static const String wholikesyou =
      "هل تريد ان تعرف من أُعجب بك؟ انتقل الى الرزمة الذهبية واشترك الآن.";

  static const String why_photo = "لماذا نريد صورة";
  static const String widower = "أرمل/ أرملة";
  static const String write_about_yourself = "اكتب بضع كلمات عن نفسك وهواياتك";
  static const String youAddedFeedbackToThApplication =
      'لقد قمت بإضافة تعليقات إلى التطبيق';

  static const String aboutAddedSuccessfully = 'تمت الإضافة بنجاح';

  static const String whereFrom = "  من أين أنت";
  static const String city = "المدينة";
  static const String min_age = "اقل عمر";
  static const String max_age = "اقصي عمر";

  static const String min_weight = "اقل وزن";
  static const String max_weight = "اقصي وزن";

  static const String min_height = "اقل طول";
  static const String max_height = "اقصي طول";

  static const String area = "  منطقة  ";
  static const String height = "طول";
  static const String weight = "وزن";
  static const String isSmoking = "هل تدخن";
  static const String yes = "نعم";
  static const String no = "لا";
  static const String error = '  لقد حدث خطأ';
  static const String logout = "تسجيل خروج";
  static const String mainly = "الرزمة : ";
  static const String marital_status = "الحالة الاجتماعية";
  static const String married = "متزوج";
  static const String muslim = "مسلم";
  static const String christian = "مسيحي";
  static const String druze = "درزي";

  static const String mayBeLater = "ربما في وقت لاحق";
  static const String me = "أنا";
  static const String msgs = "رسائل";
  static const String my_name = "اسمي";
  static const String my_personal_data = " مواصفاتي الشخصية  ";
  static const String edit_my_personal_data = "تعديل مواصفاتي الشخصية  ";
  static const String required_data = "المواصفات المطلوبه";
  static const String edit_required_data = "تعديل المواصفات المطلوبه";
  static const String deletedUsers = "المستخدمون المحذوفون";
  static const String consultant = "الخدمات الإستشارية";

  static const String new_code = "اطلب رمزًا جديدًا في الساعة 00:30";
  static const String new_couples = "ملائمات جديدة";
  static const String new_pass = "كلمة مرور جديدة";
  static const String next = "التالي";
  static const String nointernet = "Check Your Internet Connection";
  static const String not_mainly = "الذهبية";
  static const String notification = "إشعارات";
  static const String otherNotification = "إشعارات أخرى :";
  static const String appReport = "الابلاغ عن مشكلة";
  static const String payementPossibility = "  الدفع ";
  static const String bankTransfer = " التحويل المصرفي";
  static const String payPall = " باي بال  ";
  static const String changeCountry = "  تغيير الدولة  ";

  static const String payementDetails = " خطط الدفع    ";

  static const String cardNo = "    رقم البطاقة  ";
  static const String month = 'شهر';

  static const String expiryDate = "   تاريخ الانتهاء  ";
  static const String payementPass = "  رمز الحماية  ";
  static const String idNo = " رقم الهوية  ";
  static const String payNow = " ادفع الآن   ";
  static const String checkEmail = "  تم إرسال البريد الإلكتروني   ";
  static const String enterPassword = " ادخل كلمة المرور ";
  static const String accountDeleted = "  Account Deleted Successfully  ";
  static const String DEVICEID = "DEVICEID";
  static const String Name = "Name";
  static const String isFromVerification = "isFromVerification";
  static const String isInReVerification = "isInReVerification";
  static const String DEVICEIDObserver = "DEVICEIDObserver";

  static const String DEVICEIDExternal = "DEVICEIDExternal";
  static const String noLikesyet = "حتى الآن لم يعجبك أحد. ";
  static const String noRequiredyet =
      "لم يتم العثور على شخص مناسب لك حتى الآن!";
  static const String logout_confirm = "هل تريد الخروج من التطبيق !";
  static const String dismiss = " إزالة المستخدم ";
  static const String dismiss_confirm =
      "هل تريد ازالة هذا المستخدم من القائمة  !";
  static const String block = "حظر";
  static const String block_confirm = "هل تريد حظر هذا المستخدم !";
  static const String pleaseEntreUserName = 'من فضلك ادخل اسم المستخدم';
  static const String pleaseEntredescription = 'من فضلك ادخل الوصف';
  static const String pleaseEnterDetails = '   أدخل التفاصيل التالية من فضلك';
  static const String name = '   الاسم ثلاثي ';
  static const String enterPhonOtp = 'أدخل الرقم الذي وصل الى جوالك الآن';
  static const String chooseSelfie = '  اختر صورة السيلفي ';
  static const String chooseID = '  اختر صورة الهوية ';
  static const String addedDone = ' تم اختيار صورة  ';
  static const String ucClear =
      ' اذا لم تكن الصورة واضحة سيتم ابلاغك لاستبدالها ';
  static const String privPhoto =
      ' صورة السيلفي و الهوية هي لاستخدام ادارة التطبيق فقط ولا يتم نشرها على الملأ';

  static const String acceptedpic = ' تقبل الملفات بصيغة  png ,jpg ';

  ////////////////////////ERROR MESSAGES///////////////////////////////
  static const String went_wrong = "  حدث خطأ ما، حاول مرة أخرى لاحقًا  ";
  static const String fail_loading = " فشل تحميل البيانات";
  static const String no_internet = "لا يوجد اتصال بالإنترنت ";
  static const String timeout = " انتهت مهلة الاتصال ";
  static const String done = "  تم  ";

  static const String unexpected_error = "حدث خطأ غير متوقع";
  static const String resetPassDone = "  تم إعادة تعيين كلمة المرور ";
  static const String youLiked = "  لقد تمت الإضافة لقائمة الإعجاب";
  static const String youDisLiked = "  لقد تمت الإزالة من قائمة الإعجاب";
  static const String youDelete = "  تم حظر المستخدم     ";
  static const String twoImages = "  من فضلك اختر صورة       ";
  static const String deleteAccountConfirm = "هل تريد حذف حسابك       ";
  static const String resend_otp = "اعادة ارسال الكود";
  static const String changeEmail = "تغيير البريد الإلكتروني";
  static const String changePhoneNo = "تغيير رقم الهاتف  ";
  static const String url = "url";
  static const String requiredInfo = "بيانات ضرورية";
}
