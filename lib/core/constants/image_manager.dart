class ImageManager {
  static const String logo = 'assets/images/logo.png';
  static const String pink_background = 'assets/images/pinkBackground.png';
  static const String onboarding = 'assets/images/onboarding.png';
  static const String google = 'assets/images/google.png';
  static const String facebook = 'assets/images/facebook.png';

  static const String small_logo = 'assets/images/small_logo.png';
  static const String male = 'assets/images/male.png';
  static const String female = 'assets/images/female.png';
  static const String setting = 'assets/images/Setting.png';
  static const String heart = 'assets/images/Heart.svg';
  static const String home = 'assets/images/Home.svg';
  static const String chat = 'assets/images/Chat.svg';
  static const String notification = 'assets/images/Notification.svg';
  static const String account = 'assets/images/Profile.png';
  static const String galleryEdit = 'assets/images/gallery-edit.png';
  static const String congratulations = 'assets/images/congratulations.gif';

  static const String addImageDefult = 'assets/images/add_image_defult.png';
  static const String edit_pen_icon = 'assets/images/pen_edit_icon.png';
  static const String chat_colored = 'assets/images/Chat_colored.svg';
  static const String heart_colored = 'assets/images/Heart_colored.svg';
  static const String home_colored = 'assets/images/Home_colored.svg';
  static const String filter = 'assets/images/filter.svg';
  static const String heartLogo = 'assets/images/rings_logo.svg';
  static const String account_colored = 'assets/images/Profile_colored.png';

  static const String notificationLogo = 'assets/images/Notification_logo.svg';

  static const String heart_message = 'assets/images/message_heart.png';
  static const String check_mark = 'assets/images/mark.svg';
  static const String videoLogo = 'assets/images/video_icon.svg';
  static const String gifLogo = 'assets/images/gif_icon.svg';
  static const String sendLogo = 'assets/images/send.svg';

  static const String chatIcon = 'assets/images/Chat_icon.svg';
  static const String closeIcon = 'assets/images/close_icon.svg';
  static const String favIcon = 'assets/images/fav_icon.svg';
  static const String menuIcon = 'assets/images/menu.svg';
  static const String settingIcon = 'assets/images/setting_icon.svg';
  static const String verifiedIcon = 'assets/images/verified.svg';
  static const String paymentMethods = 'assets/images/paymentSvg.svg';
  static const String payPallMethod = 'assets/images/payPall.svg';
  static const String payPallPng = 'assets/images/payPallPng.png';
  static const String lock = 'assets/images/lock.svg';
  static const String lockPng = 'assets/images/lockPng.png';
  static const String visible = 'assets/images/visible_icon.svg';
  static const String nonVisible = 'assets/images/visibleOff_icon.svg';

  static const String sendIcon = 'assets/images/send_icon.svg';
  static const String profile = 'assets/images/profile.png';
  static const String profileError = 'assets/images/profileError.png';
  static const String idImage = 'assets/images/idImage.svg';
}
