import 'package:flutter/material.dart';

class ColorManager {
  ///app colors
  static const Color primaryColor = Color(0xffA12D63);
  static const Color yellowColor = Color(0xffEAC435);
  static const Color greenColor = Color(0xff2DA154);
  static const Color borderColor = Color(0xffB9B9B9);
  static const Color secondaryPinkColor = Color(0xffFFE9F3);

  static const Color darkGrey = Color(0xff515151);

  ///backgrounds
  static const Color backgroundColor = Colors.white;
  static Color backgroundDialogColor = const Color(0xffA12D63).withOpacity(0.5);
  static Color fadePinkColor = const Color(0xffFAEFFA);
  static Color splashTopColor = const Color(0xffA12D63);
  static Color splashBottomColor = const Color(0xff4E55FD).withOpacity(0.5);
  static Color pinkColor = const Color(0xffFFE9F3);

  /// text
  static const Color hintTextColor = Color(0xffB9B9B9);
  static const Color greyTextColor = Color(0xff515151);
  static const Color primaryTextColor = Color(0xffA12D63);
  static const Color whiteTextColor = Colors.white;
}
