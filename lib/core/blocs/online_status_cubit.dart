import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:zawaj/core/helper/signalr.dart';

// Events
abstract class OnlineStatusEvent {}

class UserWentOnline extends OnlineStatusEvent {
  final String userId;
  UserWentOnline(this.userId);
}

class UserWentOffline extends OnlineStatusEvent {
  final String userId;
  UserWentOffline(this.userId);
}

class OnlineUsersUpdated extends OnlineStatusEvent {
  final List<String> onlineUsers;
  OnlineUsersUpdated(this.onlineUsers);
}

// States
abstract class OnlineStatusState {}

class OnlineStatusInitial extends OnlineStatusState {}

class OnlineStatusLoaded extends OnlineStatusState {
  final List<String> onlineUserIds;
  
  OnlineStatusLoaded(this.onlineUserIds);
  
  bool isUserOnline(String userId) {
    return onlineUserIds.contains(userId);
  }
}

// Cubit
class OnlineStatusCubit extends Cubit<OnlineStatusState> {
  OnlineStatusCubit() : super(OnlineStatusInitial()) {
    _setupSignalRCallbacks();
  }

  void _setupSignalRCallbacks() {
    SignalRService.setUserOnlineCallback((userId) {
      _handleUserWentOnline(userId);
    });

    SignalRService.setUserOfflineCallback((userId) {
      _handleUserWentOffline(userId);
    });

    SignalRService.setOnlineUsersChangedCallback((onlineUsers) {
      _handleOnlineUsersUpdated(onlineUsers);
    });
  }

  void _handleUserWentOnline(String userId) {
    final currentState = state;
    if (currentState is OnlineStatusLoaded) {
      final updatedUsers = List<String>.from(currentState.onlineUserIds);
      if (!updatedUsers.contains(userId)) {
        updatedUsers.add(userId);
        emit(OnlineStatusLoaded(updatedUsers));
      }
    }
  }

  void _handleUserWentOffline(String userId) {
    final currentState = state;
    if (currentState is OnlineStatusLoaded) {
      final updatedUsers = List<String>.from(currentState.onlineUserIds);
      updatedUsers.remove(userId);
      emit(OnlineStatusLoaded(updatedUsers));
    }
  }

  void _handleOnlineUsersUpdated(List<String> onlineUsers) {
    emit(OnlineStatusLoaded(onlineUsers));
  }

  bool isUserOnline(String userId) {
    final currentState = state;
    if (currentState is OnlineStatusLoaded) {
      return currentState.isUserOnline(userId);
    }
    return false;
  }

  List<String> getOnlineUsers() {
    final currentState = state;
    if (currentState is OnlineStatusLoaded) {
      return List.from(currentState.onlineUserIds);
    }
    return [];
  }
} 